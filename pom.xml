<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.wosai</groupId>
        <artifactId>common-parent</artifactId>
        <version>2.0-SNAPSHOT</version>
    </parent>

    <groupId>com.wosai.upay</groupId>
    <artifactId>merchant-contract-parent</artifactId>
    <version>2.62.2</version>
    <packaging>pom</packaging>

    <modules>
        <module>api</module>
        <module>war</module>
    </modules>

    <name>merchant-contract-parent</name>
    <url>http://maven.apache.org</url>

    <properties>
        <nextgen.version>2.0-SNAPSHOT</nextgen.version>
        <spring4-boot.version>1.0-SNAPSHOT</spring4-boot.version>
        <core-business.version>3.7.21</core-business.version>

    </properties>


    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.wosai</groupId>
                <artifactId>spring4-boot-dependencies</artifactId>
                <version>${spring4-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.wosai.nextgen</groupId>
                <artifactId>nextgen-parent</artifactId>
                <version>${nextgen.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.3</version>
                    <configuration>
                        <target>1.8</target>
                        <source>1.8</source>
                        <encoding>UTF-8</encoding>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>2.10</version>
                </plugin>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>0.8.5</version>
                    <executions>
                        <execution>
                            <id>jacoco-initialize</id>
                            <goals>
                                <goal>prepare-agent</goal>
                                <goal>report</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>post-unit-test</id>
                            <phase>test</phase>
                            <goals>
                                <goal>report</goal>
                            </goals>
                            <configuration>
                                <dataFile>target/jacoco.exec</dataFile>
                                <outputDirectory>target/jacoco-ut</outputDirectory>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <distributionManagement>
        <repository>
            <id>central</id>
            <name>maven-virtual-dev</name>
            <url>https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>maven-virtual-dev</name>
            <url>https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev</url>
        </snapshotRepository>
    </distributionManagement>

</project>
