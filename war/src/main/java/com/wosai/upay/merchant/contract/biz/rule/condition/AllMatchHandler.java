package com.wosai.upay.merchant.contract.biz.rule.condition;

import com.wosai.upay.merchant.contract.biz.rule.SupportUtil;
import com.wosai.upay.merchant.contract.biz.rule.value.FieldHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-09-04
 */
@Component
public class AllMatchHandler extends MatchRuleHandler {

    @Autowired
    private FieldHandler fieldHandler;

    @Override
    protected void doApply(Map<String, Object> source, Object ruleDetail) {
        Map<String, Object> all = SupportUtil.getMap(ruleDetail);

        boolean allMatch = all.entrySet().stream().anyMatch(entry -> {
            Object value = fieldHandler.apply(source, null, entry.getKey());
            return entry.getValue().equals(value);
        });

        if (!allMatch) {
            throw new NotMatchException();
        }
    }

    @Override
    public int getOrder() {
        return 1;
    }

    @Override
    public String supportRule() {
        return "all_match";
    }
}
