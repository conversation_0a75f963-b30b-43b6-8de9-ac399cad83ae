package com.wosai.upay.merchant.contract.utils.guotong.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/12/11
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GuotongCommonContractRequest extends GuotongCommonOperateRequest{

    /**
     * 操作类型
     * 1:新增 2：修改（首次进件未审核通过被驳回时选择）
     */
    private String operType;
    /**
     * 法人证件类型
     * 0:身份证，1:护照，2:港澳居民往来内地通行证（回乡证），3:台湾居民往来内地通行证（台胞证），4:外国人永久居留证，5港澳居民居住证，6:台湾居民居住证，7:执行事务合伙人。不填默认为0
     */
    private String nolegalCertType;
    /**
     * 非法人结算人证件类型
     * 非法人对私时填写。0:身份证，1:护照，2:港澳居民往来内地通行证（回乡证），3:台湾居民往来内地通行证（台胞证），4:外国人永久居留证，5港澳居民居住证，6:台湾居民居住证，7:执行事务合伙人。不填默认为0
     */
    private String accCredType;
}
