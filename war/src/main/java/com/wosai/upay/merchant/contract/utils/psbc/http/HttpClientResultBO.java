package com.wosai.upay.merchant.contract.utils.psbc.http;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Description: 封装httpClient响应结果
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HttpClientResultBO implements Serializable {

    private static final long serialVersionUID = 6444298203946578179L;
    /**
     * 响应状态码
     */
    private Integer code;

    /**
     * 响应数据
     */
    private String content;



    public HttpClientResultBO(Integer code) {
        this.code = code;
    }


}
