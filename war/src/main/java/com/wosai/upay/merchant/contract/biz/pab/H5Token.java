package com.wosai.upay.merchant.contract.biz.pab;

import cn.com.agree.cipher.jwt.variant.KeyStore;
import cn.com.agree.cipher.jwt.variant.TemporaryKey;
import cn.com.agree.cipher.sm2.SM2Util;
import cn.com.agree.cipher.utils.Util;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.pingan.openbank.api.sdk.common.CAVariantJwe;
import com.pingan.openbank.api.sdk.common.helper.SdkSignature;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.exception.ContractSysException;
import com.wosai.upay.merchant.contract.utils.SnowFlakeIdGenerator;
import com.wosai.upay.merchant.contract.utils.Utils;
import com.wosai.upay.merchant.contract.utils.chinaUms.http.HttpClientResultBO;
import com.wosai.upay.merchant.contract.utils.chinaUms.http.HttpClientUtils;
import com.wosai.upay.merchant.contract.utils.pingan.HttpClientUtil;
import com.wosai.upay.merchant.contract.utils.pingan.VariantJwe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * 用于处理平安H5请求返回一个可以跳转的签约链接
 * <AUTHOR>
 */
@Component
@Slf4j
public class H5Token {

    @Value("${pingAn.h5RequestUrl}")
    private String pingAnH5RequestUrl;

    @Value("${pingAn.location}")
    private String pingAnLocation;

    @Value("${pingAn.MockUrl}")
    private String pingAnMockUrl;

    @Value("${pingAn.h5ID}")
    private String h5ID;

    private Properties properties;
    private String appId;
    private String appSecret;
    private String appPrivateKey;
    private String publicKey;

    @PostConstruct
    public void init() {
        try {
            properties = new Properties();
            properties.load(H5Token.class.getClassLoader().getResourceAsStream(pingAnLocation));
            appId = properties.getProperty("appId");
            appSecret = properties.getProperty("appSecret");
            appPrivateKey = properties.getProperty("appPrivateKey");
            publicKey = properties.getProperty("publicKey");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String getPingAnH5Url(String applyToken) {
        try {
            if(pingAnMockUrl.contains("mock-server") || !StringUtils.isEmpty(pingAnMockUrl)) {
                HttpClientResultBO resultBO;
                try {
                    resultBO = HttpClientUtils.doPost(pingAnMockUrl +"/h5/h5/token", null, JSONObject.toJSONString(applyToken));
                } catch (Exception e) {
                    log.error("请求mock服务失败",e);
                    throw new ContractBizException("请求mock服务失败"+e);
                }
                return BeanUtil.getNestedProperty(JSONObject.parseObject(resultBO.getContent(),Map.class),"content.result.h5Url").toString();
            }
            String encryptMethodSm4 = "SM4";
            String signMethodJws = "JWS";
            Map<String, String> headers = new HashMap<>();
            headers.put("x-pab-appID", appId);
            headers.put("x-pab-version", "1.9.133");
            headers.put("x-pab-global-seqno", String.valueOf(SnowFlakeIdGenerator.getInstance().nextId()));
            headers.put("x-pab-timestamp", Utils.getDateTimeString());
            headers.put("x-pab-signMethod", signMethodJws);
            headers.put("x-pab-encrypt", "true");
            headers.put("x-pab-encryptMethod", encryptMethodSm4);
            headers.put("x-pab-h5ID", h5ID);

            Map<String, String> params = new HashMap<>();
            params.put("grantType", "client_credentials");
            params.put("clientSecret", appSecret);
            String reqBody = JSON.toJSONString(params);
            // 加签请求
            String signJws = SdkSignature.sign(appId, appPrivateKey, reqBody);
            headers.put("x-pab-signature", signJws);

            // 加密请求
            TemporaryKey sm4Key = KeyStore.doGetOrStoreKey(appId, publicKey);
            String reqBodyEncrypted = CAVariantJwe.encryptJweUseKeyStore(appId, sm4Key, reqBody);


            Map<String, Object> resultMap = HttpClientUtil.sendJsonStrHttp(pingAnH5RequestUrl, reqBodyEncrypted, 10000, 10000, headers);

            // 解密应答
            if (resultMap == null || !"200".equals(resultMap.get("code"))) {
                log.error("应答不成功");
                throw new ContractSysException("获取平安H5地址异常");
            }
            String respPlain = decryptJwe((String) resultMap.get("data"), appPrivateKey);
            String resultSign = (String) ((HashMap<String, Object>) resultMap.get("Headers")).get("x-pab-signature");
            String token = JSON.parseObject(respPlain).getJSONObject("content").getJSONObject("result").getString("access_token");
            String url = JSON.parseObject(respPlain).getJSONObject("content").getJSONObject("result").getString("h5Url");

            // 验签应答 这一步好像没有必要验签吧
//            boolean verifySign = SdkSignature.verifySign(resultSign, publicKey, respPlain);

            // 拼装H5 url
            String requestTime = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_FORMAT);
            JSONObject bizBodyPlain = new JSONObject();
            JSONObject param = new JSONObject();
            bizBodyPlain.put("applyToken", applyToken);
            bizBodyPlain.put("requestTime", requestTime);

            // 报文加密
            String bodyPlainStr = bizBodyPlain.toJSONString();
            String body = CAVariantJwe.encryptJweUseKeyStore(appId, sm4Key, bodyPlainStr);

            // 报文加签
            String sign = SdkSignature.sign(appId, appPrivateKey, bodyPlainStr);
            param.put("body", body);
            param.put("sign", sign);
            param.put("appId", appId);
            param.put("encryptMethod", encryptMethodSm4);
            param.put("signMethod", "SM2");
            param.put("encrypt", true);
            param.put("token", token);

            url = url + buildPathParam(param);
            return url;
        } catch (Exception e) {
            log.error("获取平安H5地址失败", e);
            throw new ContractBizException("获取平安H5地址失败");
        }
    }

    public String decryptJwe(String jweStr, String priKeyStr) throws InvalidAlgorithmParameterException, IllegalBlockSizeException, NoSuchPaddingException, BadPaddingException, NoSuchAlgorithmException, InvalidKeyException, NoSuchProviderException {
        VariantJwe.Jwe jwe = VariantJwe.Jwe.parse(jweStr);
        String sm4Key = SM2Util.decrypt(jwe.getEncryptedKey(), priKeyStr);
        byte[] plainTextBytes = cn.com.agree.cipher.sm4.SM4Util.decrypt_Cbc_Padding(Util.hexStringToBytes(sm4Key), Util.hexStringToBytes(jwe.getIv()), Util.hexToByte(jwe.getCipherText()));
        return new String(plainTextBytes, StandardCharsets.UTF_8);
    }

    private String buildPathParam(JSONObject param) {
        StringBuilder sb = new StringBuilder("?");
        for (Map.Entry<String, Object> entry : param.entrySet()) {
            sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        return sb.substring(0, sb.length() - 1);
    }
}