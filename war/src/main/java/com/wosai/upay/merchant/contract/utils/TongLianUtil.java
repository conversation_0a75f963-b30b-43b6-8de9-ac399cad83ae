package com.wosai.upay.merchant.contract.utils;

import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.merchant.contract.exception.ContractBizException;

import java.text.DateFormat;
import java.text.SimpleDateFormat;

/**
 * <AUTHOR>
 * @date 2019-09-05
 */
public class TongLianUtil {

    /**
     * 收钱吧商户类型 0 小微  1 个体户  2 事业单位
     * 通联商户类型 4 小微  3 个体户  1 事业单位
     *
     * @param licenseType
     * @return
     */
    public static String convertLicenseType(int licenseType) {
        switch (licenseType) {
            case 0:
                return "4";
            case 1:
                return "3";
            default:
                return "1";
        }
    }

    /**
     * 收钱吧法人证件类型：1 身份证；2 港澳居民来往内地通行证； 3 台湾居民来往大陆通行证； 4 非中华人民共和国护照   5 中华人民共和国护照  6 港澳居民证 7 台湾居民证
     * 通联法人证件类型：01 身份证；05 台胞证； 04 港澳通行证 ; 03-护照; 08 临时身份证; 11 港澳台居民居住证; 99 其他
     *
     * @param idType
     * @return
     */
    public static String convertIdType(int idType) {
        switch (idType) {
            case 1:
                return "01";
            case 2:
                return "04";
            case 3:
                return "05";
            case 4:
                return "03";
            case 6:
                return "11";
            case 7:
                return "11";
            default:
                return "99";
        }
    }

    /**
     * 收钱吧法人证件类型：1 身份证；2 非中华人民共和国护照； 3 台胞证； 4 港澳通行证   5 中华人民共和国护照  6 港澳居民证 7 台湾居民证
     * 通联法人证件类型：0 身份证；2 护照 5 台胞证； 6 港澳通行证
     *
     * @param idType
     * @return
     */
    public static String convertVerifyThreeElementsIdType(int idType) {
        switch (idType) {
            case 1:
                return "0";
            case 2:
                return "2";
            case 3:
                return "5";
            case 4:
                return "6";
            default:
                throw new ContractBizException("id_type : " + idType + " 不合法");
        }
    }

    /**
     * 身份证：中国
     * <p>
     * 港澳居民往来内地通行证：中国
     * <p>
     * 台湾居民往来内地通行证：中国台湾
     * <p>
     * 中国护照：中国
     * <p>
     * 外籍护照：中国    -> 是的没看错
     *
     * @param idType
     * @return
     */
    public static String convertVerifyThreeElementsIdTypeV2(Integer idType) {
        switch (idType) {
            case 1:
                return "中国";
            case 2:
                return "中国";
            case 3:
                return "中国台湾";
            case 4:
                return "中国";
            case 5:
                return "中国";
            default:
                throw new ContractBizException("id_type : " + idType + " 不合法");
        }
    }

    /**
     * 身份证号倒数第二位男单女双；其他证件类型默认 女
     *
     * @param idType
     * @param identityNumber
     * @return 1 男; 2 女
     */
    public static String convertIdentityToGender(Integer idType, String identityNumber) {
        if (1 != idType) {
            return "2";
        }
        Integer num = Integer.valueOf(identityNumber.charAt(identityNumber.length() - 2));
        if (num % 2 == 0) {
            return "2";
        }
        return "1";
    }

    /**
     * 收钱吧银行卡类型  1 对私  2 对公
     * 通联银行卡类型  0 对私  1 对公
     *
     * @param bankAccountType
     * @return
     */
    public static String convertBankAccountType(int bankAccountType) {
        switch (bankAccountType) {
            case 1:
                return "0";
            case 2:
                return "1";
            default:
                throw new ContractBizException("bankAccountType : " + bankAccountType + " 不合法");
        }
    }

    /**
     * 收钱吧银行卡类型  1 对私  2 对公
     * 通联卡折 类型  00 借记卡  1 存折
     *
     * @param bankAccountType
     * @return
     */
    public static String convertBankAccountTypeV2(Integer bankAccountType) {
//        switch (bankAccountType) {
//            case 1:
//                return "00";
//            case 2:
//                return "01";
//            default:
//                throw new ContractBizException("bankAccountType : " + bankAccountType + " 不合法");
//        }
        return "00";
    }


    public static ThreadLocal<DateFormat> simpleDateFormat = new ThreadLocal<DateFormat>() {
        @Override
        protected SimpleDateFormat initialValue() {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        }
    };

    /**
     * 收钱吧法人类型  1 对私  2 对公
     *
     * @param bankAccountType
     * @return
     */
    public static String convertLegalPersonType(int bankAccountType) {
        switch (bankAccountType) {
            case 1:
                return "LEGAL_PERSON";
            case 2:
                return "CONTROLLER";
            default:
                throw new ContractBizException("LegalPersonType : " + bankAccountType + " 不合法");
        }
    }

    /**
     * ********-******** -> 2023-07-15
     *
     * @param expire
     * @return
     */
    public static String convertExpire(String expire) {
        try {
            if (WosaiStringUtils.isEmpty(expire)) {
                return expire;
            }
            String[] arrays = expire.split("-");
            if (arrays.length != 2) {
                return expire;
            }
            expire = arrays[1];
            if (expire.contains("长期")) {
                return "9999-12-13";
            }
            String year = expire.substring(0, 4);
            String month = expire.substring(4, 6);
            String day = expire.substring(6, 8);
            return String.format("%s-%s-%s", year, month, day);
        } catch (Exception e) {
            throw new ContractBizException("解析证件有效期异常 " + expire, e);
        }
    }

    public static String convertExpireChn(String expire) {
        try {
            if (WosaiStringUtils.isEmpty(expire)) {
                return expire;
            }
            String[] arrays = expire.split("-");
            if (arrays.length != 2) {
                return expire;
            }
            expire = arrays[1];
            if (expire.contains("99991231")) {
                return "长期";
            }
            String year = expire.substring(0, 4);
            String month = expire.substring(4, 6);
            String day = expire.substring(6, 8);
            return String.format("%s-%s-%s", year, month, day);
        } catch (Exception e) {
            throw new ContractBizException("解析证件有效期异常 " + expire, e);
        }
    }

}
