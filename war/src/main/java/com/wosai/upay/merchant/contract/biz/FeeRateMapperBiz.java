package com.wosai.upay.merchant.contract.biz;

import com.alibaba.fastjson.JSONObject;
import com.wosai.upay.merchant.contract.dao.FeeRateMapperDAO;
import com.wosai.upay.merchant.contract.model.entity.FeeRateMapperDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;

/**
 * fuyou费率模板映射biz
 * <AUTHOR>
 */
@Slf4j
@Component
public class FeeRateMapperBiz {

    private static FeeRateMapperDAO feeRateMapperDAO;

    @Autowired
    private ApplicationContext applicationContext;

    @PostConstruct
    private void init() {
        this.feeRateMapperDAO = applicationContext.getBean(FeeRateMapperDAO.class);
    }

    /**
     * 新增富友费率模板映射
     * @param feeRateTag
     * @param provider
     * @param fuyouSetCd
     * @param feeRateDetail
     * @return
     */
    public static FeeRateMapperDO addFeeRateMapper(String feeRateTag, Integer provider, String fuyouSetCd, Map feeRateDetail){
        FeeRateMapperDO feeRateMapper = new FeeRateMapperDO();
        try {
            feeRateMapper.setFeeRateTag(feeRateTag);
            feeRateMapper.setProvider(String.valueOf(provider));
            feeRateMapper.setFuyouSetCd(fuyouSetCd);
            feeRateMapper.setStatus(1);
            feeRateMapper.setFeeRateDetail(JSONObject.toJSONString(feeRateDetail));
            feeRateMapperDAO.insertFeeRateMapper(feeRateMapper);
        } catch (Exception e){
            log.error("新增费率模板失败:{},{}", feeRateTag, fuyouSetCd, e);
        }
        return getFeeRateMapperByFeeRateTag(feeRateTag);
    }

    public static FeeRateMapperDO getFeeRateMapperByFeeRateTag(String feeRateTag){
        try {
            return feeRateMapperDAO.getFeeRateMapperByFeeRateTag(feeRateTag);
        } catch (Exception e) {
            log.error("根据标记查询费率模板失败:{}", feeRateTag, e);
        }
        return null;
    }

    public static FeeRateMapperDO getFeeRateMapperBySetCd(String setCd){
        try {
            return feeRateMapperDAO.getFeeRateMapperBySetCd(setCd);
        } catch (Exception e){
            log.error("根据费率模板查询费率模板失败:{}", setCd, e);
        }
        return null;
    }


}
