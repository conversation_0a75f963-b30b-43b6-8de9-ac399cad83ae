package com.wosai.upay.merchant.contract.utils;

import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayRequest;
import com.alipay.api.AlipayResponse;
import com.alipay.api.request.AlipayOpenSpImageUploadRequest;
import com.wosai.upay.merchant.contract.constant.Constant;
import com.wosai.upay.merchant.contract.exception.ContractException;
import com.wosai.upay.merchant.contract.model.AliCommResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class AliDirectResponseUtil {
    @Autowired
    private ResultMsgUtil resultMsgUtil;

    public <T, U> AliCommResponse<T, U> convertAliResDirect(Object request, AlipayResponse response) {
        log.info("convertAliResDirect request :{}", JSONObject.toJSONString(request));
        log.info("convertAliResDirect response :{}", JSONObject.toJSONString(response));
        int code = 200;
        String message = "";
        try {
            if (response != null) {
                if (response.isSuccess()) {
                    message = "SUCCESS";
                } else {
                    final String subCode = response.getSubCode();
                    throw new ContractException(resultMsgUtil.judgeResultCode("alipayDirect", null, subCode), response.getSubMsg());
                }
            }
        } catch (ContractException e) {
            log.error("ali_pay_open_agent  ContractException", e);
            code = e.getCode();
            message = e.getMessage();
        } catch (Exception e) {
            log.error("ali_pay_open_agent  Exception", e);
            code = Constant.RESULT_CODE_SYSTEM_EXCEPTION;
            message = e.getMessage();
            if (e.getCause() != null) {
                message = e.getCause().getMessage();
            }
        }
        AliCommResponse result = new AliCommResponse<T, U>();
        result.setCode(code);
        result.setMessage(message);
        //job反序列化解决方案
        result.setReq(request);
        result.setResp(response);
        log.info("convertAliResDirect result : {}", JSONObject.toJSONString(result));
        return result;
    }
}
