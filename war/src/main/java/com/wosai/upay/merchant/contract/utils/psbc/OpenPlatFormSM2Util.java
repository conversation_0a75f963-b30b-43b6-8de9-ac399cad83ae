package com.wosai.upay.merchant.contract.utils.psbc;


import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.asn1.gm.GMNamedCurves;
import org.bouncycastle.asn1.gm.GMObjectIdentifiers;
import org.bouncycastle.asn1.x9.X9ECParameters;
import org.bouncycastle.jcajce.provider.asymmetric.ec.BCECPrivateKey;
import org.bouncycastle.jcajce.provider.asymmetric.ec.BCECPublicKey;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.jce.spec.ECParameterSpec;
import org.bouncycastle.jce.spec.ECPrivateKeySpec;
import org.bouncycastle.jce.spec.ECPublicKeySpec;
import org.bouncycastle.math.ec.ECPoint;
import org.bouncycastle.util.encoders.Hex;
import org.springframework.util.ObjectUtils;

import java.math.BigInteger;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;


/**
 * 外包商户改造为国密
 */


@Slf4j

public class OpenPlatFormSM2Util {

    private static BouncyCastleProvider provider;

    private static ECParameterSpec ecParameterSpec;

    private static KeyFactory keyFactory;

    private static X9ECParameters parameters;
    /**
     * 加签
     *
     * @param plainText
     * @param prvKey
     * @return
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeySpecException
     * @throws InvalidKeyException
     * @throws SignatureException
     */

    private static final com.fasterxml.jackson.databind.ObjectMapper objectMapper = new ObjectMapper();


// 收钱吧

//公钥027cc71c023764bd698e5014b9fb94d551891f24cf58ed5e6d80253730f0a63063 同步验签 和 数据加密

//私钥9f23df61e4684889367e4bb9bea624455b5e4845d21a5805ba2854f1af510474  进件加签

    static {

        try {

            provider = new BouncyCastleProvider();

            parameters = GMNamedCurves.getByName("sm2p256v1");

            ecParameterSpec = new ECParameterSpec(parameters.getCurve(), parameters.getG(), parameters.getN(), parameters.getH());

            keyFactory = KeyFactory.getInstance("EC", provider);

        } catch (Exception e) {

            e.printStackTrace();

        }

    }

    /**
     * <AUTHOR>
     * @Description 按照ASCII码递增排序
     * @Date 2021/4/8 11:14
     * @Params map
     * @Return java.lang.String
     */
    public static String asciiTreeMap(TreeMap<String, Object> map) {
        StringBuffer sb = new StringBuffer();
        for (String key : map.keySet()) {
            if (!key.equals("sign")) {
                Object value = map.get(key);
                if (!ObjectUtils.isEmpty(value)) {
                    sb.append("&").append(key).append("=").append(value);
                }
            }
        }
        return sb.toString().substring(1);
    }

    public static String sing(String plainText, String prvKey) throws NoSuchAlgorithmException, InvalidKeySpecException, InvalidKeyException, SignatureException {
        //排序
        final Map map = JSONObject.parseObject(plainText, Map.class);
        TreeMap treeMap = objectMapper.convertValue(map, TreeMap.class);
        String reqStr = asciiTreeMap(treeMap);

        //创建签名对象

        Signature signature = Signature.getInstance(GMObjectIdentifiers.sm2sign_with_sm3.toString(), new BouncyCastleProvider());

        BigInteger bigInteger = new BigInteger(prvKey, 16);

        BCECPrivateKey privateKey = (BCECPrivateKey) keyFactory.generatePrivate(new ECPrivateKeySpec(bigInteger, ecParameterSpec));

        signature.initSign(privateKey);

        signature.update(reqStr.getBytes());

        return java.util.Base64.getEncoder().encodeToString(signature.sign());

    }


    /**
     * 验签
     *
     * @param plainText
     * @param signatureValue
     * @param pubKey
     * @return
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeySpecException
     * @throws InvalidKeyException
     * @throws SignatureException
     */

    public static boolean verify(String plainText, String signatureValue, String pubKey) throws NoSuchAlgorithmException, InvalidKeySpecException, InvalidKeyException, SignatureException {

        //创建签名对象

        Signature signature = Signature.getInstance(GMObjectIdentifiers.sm2sign_with_sm3.toString(), new BouncyCastleProvider());

        ECPoint ecPoint = parameters.getCurve().decodePoint(Hex.decode(pubKey));

        BCECPublicKey key = (BCECPublicKey) keyFactory.generatePublic(new ECPublicKeySpec(ecPoint, ecParameterSpec));

        signature.initVerify(key);

        signature.update(plainText.getBytes());

        return signature.verify(java.util.Base64.getDecoder().decode(signatureValue));

    }

}



	

	



	      