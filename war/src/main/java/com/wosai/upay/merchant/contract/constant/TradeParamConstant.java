package com.wosai.upay.merchant.contract.constant;

/**
 * @Description:
 * <AUTHOR>
 * @Date: 2021/4/8 2:08 下午
 */
public class TradeParamConstant {

    public static final String TRADE_PARAMS_KEY = "chinaums_trade_params";
    //toC商户号
    public static final String TOC_MCH_CODE = "mch_code";
    //toC终端号
    public static final String TOC_TERM_CODE = "term_code";
    //toB商户号
    public static final String TOB_MCH_CODE = "csb_mch_code";
    //toB终端号
    public static final String TOB_TERM_CODE = "csb_term_code";

    public static final String TRADE = "trade";

    public static final String YS_TRADE_PARAMS="ys_trade_params";


    //二维码终端ID
    public static final String CGBBANK_PRE_TERMINAL_ID="pre_terminal_id";
    //扫码王终端ID
    public static final String CGBBANK_TERMINAL_ID="terminal_id";
    public static final String PROVIDER_MCH_ID="provider_mch_id";
    //经度
    public static final String LONGITUDE="longitude";
    //纬度
    public static final String LATITUDE="latitude";

    public static final String CGB_TRADE_PARAMS_KEY="cgb_trade_params";


    //华夏银行参数
    public static final String HX_TRADE_PARAMS_KEY="hxbank_trade_params";
    //服务商商户编号
    public static final String HX_PROVIDER_SERVICE_ID="provider_service_id";
    //商户APPID
    public static final String HX_DEVELOP_APP_ID="develop_app_id";
    //商户号
    public static final String HX_PROVIDER_MCH_ID="provider_mch_id";
    //微信子商户号
    public static final String HX_WEIXIN_SUB_MCH_ID="weixin_sub_mch_id";
    //支付宝子商户号
    public static final String HX_ALIPAY_SUB_MCH_ID="alipay_sub_mch_id";
    //终端编号
    public static final String HX_PROVIDER_TERM_ID="provider_term_id";




}
