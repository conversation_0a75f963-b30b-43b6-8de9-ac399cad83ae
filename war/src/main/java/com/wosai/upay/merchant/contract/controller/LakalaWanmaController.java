package com.wosai.upay.merchant.contract.controller;

import com.wosai.upay.core.exception.CoreUnknownException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Controller
@RequestMapping("/lakalawanma")
public class LakalaWanmaController {
	private static Logger logger = LoggerFactory.getLogger(LakalaController.class);
    
    @RequestMapping(value="/batchContractMerchantCallback", method= RequestMethod.POST)
    @ResponseBody
    public void batchContractMerchantCallback(HttpServletRequest request, HttpServletResponse response) throws CoreUnknownException, IOException {

    }
}
