package com.wosai.upay.merchant.contract.biz;

import avro.shaded.com.google.common.collect.Lists;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.service.StoreService;
import com.wosai.upay.bank.info.api.model.District;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.core.service.MerchantBankService;
import com.wosai.upay.core.service.MerchantBusinessLicenseService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.merchant.contract.config.ApolloParamsConfig;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.MerchantProviderParams;
import com.wosai.upay.merchant.contract.model.provider.ChannelParam;
import com.wosai.upay.merchant.contract.model.terminal.*;
import com.wosai.upay.merchant.contract.repository.DataRepository;
import com.wosai.upay.merchant.contract.service.ProviderTradeParamsService;
import com.wosai.upay.merchant.contract.utils.OffsetUtil;
import com.wosai.upay.merchant.contract.utils.Utils;
import com.wosai.upay.merchant.contract.utils.ali.AliCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.wosai.upay.merchant.contract.constant.LakalaWanmaBusinessFileds.*;

/**
 * @Description: 259号文件报备新增字段
 * <AUTHOR>
 * @Date 2022/3/1 14:37
 */
@Component
@Slf4j
public class ContractWith259Biz {

    @Autowired
    private ApolloParamsConfig apolloParamsConfig;

    @Autowired
    private CommonBiz commonBiz;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;

    @Autowired
    private MerchantBankService merchantBankService;

    @Autowired
    private WeiXinRuleBiz weiXinRuleBiz;

    @Autowired
    private StoreService storeService;

    @Autowired
    private DataRepository repository;

    private Dao<Map<String, Object>> alyDistrictDao;

    @Autowired
    private ProviderTradeParamsService providerTradeParamsService;

    @Autowired
    DistrictBiz districtBiz;

    @PostConstruct
    private void init() {
        alyDistrictDao = repository.getAlyDistrictDao();
    }


    private static final int DEFAULT_TRUE = 1;

    /**
     * 微信支付分配给收单服务商的名称
     */
    private Map<String, Object> getChannelNameMap() {
        return apolloParamsConfig.getMap("channel_name", "{}");
    }

    /**
     * 与银行或机构合作的伙伴标识名称，
     * 填写该合作伙伴在支付宝的pid对应
     * 的名称
     */
    private String getSourceName() {
        return apolloParamsConfig.getString("source_name", "上海收钱吧互联网科技股份有限公司");
    }


    /**
     * 申请服务
     */
    public static String SERVICE_CODES = "JSAPI,APPLET,MICROPAY,APP,MWEB,PAP,AUTH";


    public Map<String, Object> contractWxWith259Info(Map contextParam, ChannelParam channelParam) {
        //从上下文获取商户信息
        Map<String, Object> merchant = (Map<String, Object>) Optional.ofNullable(contextParam).map(x -> x.get("merchant"))
                .orElseThrow(() -> new ContractBizException("商户信息不存在"));
        //从上下文中获取银行卡信息
        Map<String, Object> bankAccount = (Map<String, Object>) Optional.ofNullable(contextParam).map(x -> x.get("bankAccount"))
                .orElseThrow(() -> new ContractBizException("银行卡信息不存在"));
        //从上下文中获取营业执照信息
        final Map<String, Object> license = (Map<String, Object>) Optional.ofNullable(contextParam).map(x -> x.get("merchantBusinessLicense"))
                .orElseThrow(() -> new ContractBizException("营业执照信息不存在"));
        //地址信息
        final AddressInfo addressInfo = buildAddressInfoForWx(merchant);
        //银行结算卡信息
        final BankcardInfo bankcardInfo = buildBankcardInfo(bankAccount);
        //收钱吧商户类型  0=无营业执照、1=个体工商户营业执照、2=企业营业执照、3=事业单位法人证书、4=民办非企业单位登记证书、5=社会团体法人登记证书、6=基金会法人登记证书、7=律师事务所执业许可证、8=宗教活动场所法人登记证书、9=农民专业合作社
        int type = BeanUtil.getPropInt(license, MerchantBusinessLicence.TYPE);
        //商户证件编号 小微：结算人证件号   个体/组织：营业证照统一社会信用代码
        final String businessLicense = Objects.equals(0, type) ? BeanUtil.getPropString(bankAccount, MerchantBankAccount.IDENTITY) : BeanUtil.getPropString(license, MerchantBusinessLicence.NUMBER);
        final String businessLicenseType = getBusinessLicenseType(type);
        final String channelName = BeanUtil.getPropString(getChannelNameMap(), channelParam.getChannel_no());
        return CollectionUtil.hashMap("channel_name", StringUtils.isEmpty(channelName) ? "上海收钱吧互联网科技股份有限公司" : channelName,
                "service_codes", SERVICE_CODES,
                "address_info", JSONObject.toJSONString(addressInfo),
                "business_license", businessLicense,
                "business_license_type", businessLicenseType,
                "bankcard_info", JSONObject.toJSONString(bankcardInfo),
                "up_merchant_id", getUpMerchantId(BeanUtil.getPropString(merchant, Merchant.SN), channelParam)
        );
    }

    /**
     * 获取lklV3或者银联万码对应的银联商户号
     *
     * @param merchantSn
     * @param channelParam
     * @return
     */
    public String getUpMerchantId(String merchantSn, ChannelParam channelParam) {
        //lklV3或者银联万码
        if (Objects.equals(channelParam.getProvider(), String.valueOf(ProviderEnum.PROVIDER_LAKALA_V3.getValue())) ||
                Objects.equals(channelParam.getProvider(), String.valueOf(ProviderEnum.PROVIDER_DIRECT_UNIONPAY.getValue())) ||
                Objects.equals(channelParam.getProvider(), String.valueOf(MerchantProviderParams.PROVIDER_LKL_ORG))) {
            return getUnionMerchantId(merchantSn, String.valueOf(ProviderEnum.PROVIDER_LAKALA_V3.getValue()));
        }
        return getUnionMerchantId(merchantSn, channelParam.getProvider());
    }

    /**
     * 商户在银联的商户号
     *
     * @param merchantSn 商户号
     * @return
     */
    private String getUnionMerchantId(String merchantSn, String provider) {
        // 海科取云闪付参数的 pay_merchant_id
        if (Objects.equals(String.valueOf(ProviderEnum.PROVIDER_HAIKE.getValue()), provider)) {
            Map<String, Object> merchantProviderParams = providerTradeParamsService.getMerchantProviderParams(merchantSn, null, PaywayEnum.UNIONPAY.getValue(), provider);
            return BeanUtil.getPropString(merchantProviderParams, MerchantProviderParams.PAY_MERCHANT_ID);
        }
        Map<String, Object> merchantProviderParams = providerTradeParamsService.getMerchantProviderParams(merchantSn, null, PaywayEnum.ACQUIRER.getValue(), provider);
        return BeanUtil.getPropString(merchantProviderParams, MerchantProviderParams.PROVIDER_MERCHANT_ID);
    }


    /**
     * 根据上下文信息获取259终端改造所需要信息
     *
     * @param contextParam
     * @return
     */
    public Map<String, Object> updateWxWith259Info(Map contextParam, ChannelParam channelParam) {
        final Map<String, Object> info = contractWxWith259Info(contextParam, channelParam);
        //TODO 代产品定义
        info.putIfAbsent("pay_ctrl", null);
        return info;
    }

    /**
     * 根据微信子商户号获取259终端改造所需要信息
     *
     * @param subMchId
     * @return
     */
    public Map<String, Object> updateWxWith259Info(String subMchId, ChannelParam channelParam) {
        final Map<String, Object> context = Maps.newHashMap();
        //根据微信子商户号获取商户信息
        Map merchantProviderParams = providerTradeParamsService.getMerchantProviderParamsByPayMerchantId(subMchId);
        if (Objects.isNull(merchantProviderParams)) {
            throw new ContractBizException("商户还未报备");
        }
        final String merchantSn = BeanUtil.getPropString(merchantProviderParams, MerchantProviderParams.MERCHANT_SN);
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        context.put("merchant", merchant);
        //获取营业执照信息
        final String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        final Map<String, Object> license = merchantBusinessLicenseService.getBusinessLicenseByMerchantId(merchantId);
        context.put("merchantBusinessLicense", license);
        //获取当前再用银行卡信息
        ListResult listResult = merchantBankService.findMerchantBankAccountPres(null,
                CollectionUtil.hashMap("merchant_id", BeanUtil.getPropString(merchant, com.wosai.upay.common.dao.DaoConstants.ID), "default_status", 1));

        Map bankAccount = null;
        if (listResult != null && listResult.getTotal() != 0) {
            bankAccount = listResult.getRecords().get(0);
        }
        context.put("bankAccount", bankAccount);
        final Map<String, Object> info = contractWxWith259Info(context, channelParam);
        //TODO 代产品定义
        info.putIfAbsent("pay_ctrl", null);
        return info;
    }


    /**
     * 根据上下文信息获取259终端改造所需要信息
     *
     * @return
     */
    public Map<String, Object> queryWxWith259Info(ChannelParam channelParam) {
        final String channelName = BeanUtil.getPropString(getChannelNameMap(), channelParam.getChannel_no());
        final Map queryWxWith259InfoMap = CollectionUtil.hashMap("channel_name", StringUtils.isEmpty(channelName) ? "上海收钱吧互联网科技股份有限公司" : channelName);
        return queryWxWith259InfoMap;
    }

    /**
     * 构建支付宝259信息
     *
     * @param contextParam
     * @param channelParam
     * @return
     */
    public Map<String, Object> contractAliWith259Info(Map contextParam, ChannelParam channelParam) {
        //从上下文获取商户信息
        Map<String, Object> merchant = (Map<String, Object>) Optional.ofNullable(contextParam).map(x -> x.get("merchant"))
                .orElseThrow(() -> new ContractBizException("商户信息不存在"));
        //从上下文中获取银行卡信息
        Map<String, Object> bankAccount = (Map<String, Object>) Optional.ofNullable(contextParam).map(x -> x.get("bankAccount"))
                .orElseThrow(() -> new ContractBizException("银行卡信息不存在"));
        //从上下文中获取营业执照信息
        final Map<String, Object> license = (Map<String, Object>) Optional.ofNullable(contextParam).map(x -> x.get("merchantBusinessLicense"))
                .orElseThrow(() -> new ContractBizException("营业执照信息不存在"));
        //地址信息
        final AddressInfo addressInfo = buildAddressInfoForAli(merchant);
        //银行结算卡信息
        final BankcardInfo bankcardInfo = buildBankcardInfo(bankAccount);

        final Map<String, Object> map = CollectionUtil.hashMap("source_name", getSourceName(),
                "address_info", Lists.newArrayList(addressInfo),
                "bankcard_info", Lists.newArrayList(bankcardInfo),
                "indirect_level", "INDIRECT_LEVEL_M3",
                "up_merchant_id", getUpMerchantId(BeanUtil.getPropString(merchant, Merchant.SN), channelParam)
        );

        //收钱吧商户类型  0=无营业执照、1=个体工商户营业执照、2=企业营业执照、3=事业单位法人证书、4=民办非企业单位登记证书、5=社会团体法人登记证书、6=基金会法人登记证书、7=律师事务所执业许可证、8=宗教活动场所法人登记证书、9=农民专业合作社
        int type = BeanUtil.getPropInt(license, MerchantBusinessLicence.TYPE);
        boolean forceMicro = BeanUtil.getPropBoolean(contextParam, "forceMicro", false);
        boolean isMicro = Objects.equals(type, 0) || forceMicro;
        if (!isMicro) {
            //商户证件编号 小微：结算人证件号   个体/组织：营业证照统一社会信用代码
            final String businessLicense = Objects.equals(0, type) ? BeanUtil.getPropString(bankAccount, MerchantBankAccount.IDENTITY) : BeanUtil.getPropString(license, MerchantBusinessLicence.NUMBER);
            final String businessLicenseType = getBusinessLicenseType(type);
            map.put("business_license", businessLicense);
            map.put("business_license_type", businessLicenseType);
        }
        final ContactInfo contactInfo = new ContactInfo();
        contactInfo.setTag(Lists.newArrayList("06"));
        contactInfo.setType("LEGAL_PERSON");
        contactInfo.setMobile(commonBiz.getMerchantContactPhoneFromExtra(merchant));
        if (isMicro) {
            contactInfo.setName(BeanUtil.getPropString(bankAccount, MerchantBankAccount.HOLDER));
            contactInfo.setIdCardNo(BeanUtil.getPropString(bankAccount, MerchantBankAccount.IDENTITY));
        } else {
            contactInfo.setName(BeanUtil.getPropString(license, MerchantBusinessLicence.LEGAL_PERSON_NAME));
            contactInfo.setIdCardNo(BeanUtil.getPropString(license, MerchantBusinessLicence.LEGAL_PERSON_ID_NUMBER));
        }
        map.put("contact_info", Lists.newArrayList(contactInfo));
        return map;
    }

    /**
     * 更新支付宝信息
     *
     * @param contextParam
     * @param channelParam
     * @return
     */
    public Map<String, Object> updateAliWith259Info(Map contextParam, ChannelParam channelParam) {
        final Map<String, Object> contractAliWith259Info = contractAliWith259Info(contextParam, channelParam);
        contractAliWith259Info.putIfAbsent("merchant_state", null);
        contractAliWith259Info.putIfAbsent("pay_ctrl", null);
        return contractAliWith259Info;
    }


    /**
     * 创建省市区信息
     *
     * @param merchant 商户信息
     * @return
     */
    public AddressInfo buildAddressInfoForWx(Map<String, Object> merchant) {
        final AddressInfo addressInfo = new AddressInfo();
        District district = commonBiz.getDistrictCode(merchant);
        //商户所在省
        addressInfo.setProvinceCode(district.getProvince_code());
        //商户所在地市
        addressInfo.setCityCode(district.getCity_code());
        //商户所在市县
        addressInfo.setDistrictCode(district.getCode());
        //商户详细地址
        addressInfo.setAddress(Utils.substring(getStreetAddressFromExtra(merchant), 30));
        if (WosaiStringUtils.isEmpty(addressInfo.getAddress())) {
            throw new ContractBizException("商户缺少地址信息");
        }
        return addressInfo;
    }

    /**
     * 创建省市区信息
     *
     * @param merchant 商户信息
     * @return
     */
    public AddressInfo buildAddressInfoForAli(Map<String, Object> merchant) {
        final AddressInfo addressInfo = new AddressInfo();
        District district = commonBiz.getDistrictCode(merchant);
        String provinceCode = district.getProvince_code();
        String cityCode = district.getCity_code();
        String districtCode = district.getCode();

        // 9000结尾说明是省直辖的县级市，其实没有第二级的code
        if (cityCode.endsWith("9000")) {
            cityCode = districtCode;
        }

        // 为什么写以下这么复杂的逻辑，是因为收钱吧和支付宝使用的省市区数据并不完全统一，有些省市区支付宝不支持，
        // 所以需要到支付宝支持的省市区重新匹配一遍
        Criteria cityQuery = Criteria.where("code").is(cityCode);
        Map<String, Object> alyCity = alyDistrictDao.filter(cityQuery).fetchOne();
        Criteria districtQuery = Criteria.where("code").is(districtCode);
        Map<String, Object> alyDistrictMap = alyDistrictDao.filter(districtQuery).fetchOne();
        //二级城市查不到 随机取省份下面的一个上送 晓彬 定下的规则
        if (CollectionUtils.isEmpty(alyCity)) {
            cityQuery = Criteria.where("parent_code").is(provinceCode);
            alyCity = alyDistrictDao.filter(cityQuery).fetchOne();
            cityCode = WosaiMapUtils.getString(alyCity, "code");
            districtQuery = Criteria.where("parent_code").is(cityCode);
            alyDistrictMap = alyDistrictDao.filter(districtQuery).fetchOne();
            districtCode = WosaiMapUtils.getString(alyDistrictMap, "code");
        }
        //三级地区查不到随机取城市下面的一个上送 晓彬 定下的规则
        if (CollectionUtils.isEmpty(alyDistrictMap)) {
            districtQuery = Criteria.where("parent_code").is(cityCode);
            alyDistrictMap = alyDistrictDao.filter(districtQuery).fetchOne();
            districtCode = WosaiMapUtils.getString(alyDistrictMap, "code");
        }


        //商户所在省
        addressInfo.setProvinceCode(provinceCode);
        //商户所在地市
        addressInfo.setCityCode(cityCode);
        //商户所在市县
        addressInfo.setDistrictCode(districtCode);
        //商户详细地址
        addressInfo.setAddress(getStreetAddressFromExtra(merchant));
        return addressInfo;
    }


    /**
     * 从merchant中的extra中偏移后的地址
     */
    public String getStreetAddressFromExtra(Map merchant) {
        OffsetUtil.OffsetDistrict offsetDistrict = OffsetUtil.getOffsetDistrict(merchant);
        String address = offsetDistrict.getStreet_address();
        if (!StringUtils.isEmpty(address) && address.length() >= 6) {
            return address;
        }
        return Utils.substring(BeanUtil.getPropString(merchant, Merchant.STREET_ADDRESS), 30);
    }


    /**
     * 银行卡信息
     *
     * @param bankAccount
     * @return
     */
    public BankcardInfo buildBankcardInfo(Map<String, Object> bankAccount) {
        final BankcardInfo bankcardInfo = new BankcardInfo();
        //银行卡号
        bankcardInfo.setCardNo(BeanUtil.getPropString(bankAccount, MerchantBankAccount.NUMBER));
        //银行卡持卡人姓名
        bankcardInfo.setCardName(BeanUtil.getPropString(bankAccount, MerchantBankAccount.HOLDER));
        return bankcardInfo;
    }


    /**
     * 商户在收单机构的商户号
     *
     * @param merchantSn 商户号
     * @return
     */
    private String getAcquireMerchantId(String merchantSn, String provider) {
        //商户在收单机构的商户号
        final Map<String, Object> merchantProviderParams = providerTradeParamsService.getMerchantProviderParams(merchantSn, null, 0, provider);
        //商户号
        return BeanUtil.getPropString(merchantProviderParams, MerchantProviderParams.PAY_MERCHANT_ID);
    }


    /**
     * 商户证件类型，取值范围：
     * <p>
     * NATIONAL_LEGAL：营业执照；
     * <p>
     * NATIONAL_LEGAL_MERGE: 营 业
     * <p>
     * 执照(多证合一)；
     * <p>
     * INST_RGST_CTF：事业单位法人证书；
     * <p>
     * IDENTITY_CARD：个人身份证；
     *
     * @param type
     * @return
     */
    private String getBusinessLicenseType(int type) {
        switch (type) {
            case 0:
                return "IDENTITY_CARD";
            case 3:
                return "INST_RGST_CTF";
            default:
                return "NATIONAL_LEGAL_MERGE";
        }
    }


    /**
     * 构建终端采集信息
     *
     * @param dto         绑定参数
     * @param provider    收单机构对应在收钱吧的映射
     * @param operationId 本次操作标识，取值范围： 00：新增； 01：修改； 02：注销；（注销时，仅需上送 device_id 字段）
     * @return
     */
    public AliTermInfoRequest buildAliTermInfoRequest(BaseTermInfoDTO dto, String provider, String operationId) {
        //收单机构微信相关基础参数
        final AliTermInfoRequest request = new AliTermInfoRequest();
        String merchantSn = dto.getMerchantSn();
        String storeSn = dto.getStoreSn();
        if (StringUtils.isEmpty(merchantSn) || StringUtils.isEmpty(storeSn)) {
            throw new ContractBizException(String.format("商户号%s或者门店号%s不能为空", merchantSn, storeSn));
        }
        //收钱吧商户在收单机构的商户号 拉卡拉收单机构有1016(万码) 1033(拉卡拉) 两个支付源
        String realProvider = Objects.equals(provider, String.valueOf(ProviderEnum.PROVIDER_DIRECT_UNIONPAY.getValue())) || Objects.equals(provider, String.valueOf(MerchantProviderParams.PROVIDER_LKL_ORG)) ? String.valueOf(ProviderEnum.PROVIDER_LAKALA_V3.getValue()) : provider;
        String realMerchantSn = WosaiMapUtils.getString(providerTradeParamsService.getMerchantProviderParamsByPayMerchantId(dto.getSubMchId()), MerchantProviderParams.MERCHANT_SN);
        final String acquireMerchantId = getAcquireMerchantId(realMerchantSn, realProvider);
        request.setExternalId(acquireMerchantId);
        request.setOperationId(operationId);
        request.setTerminalId(dto.getDeviceId());
        request.setSubMerchantId(dto.getSubMchId());
        // 本次操作标识，取值范围： 00：新增； 01：修改； 02：注销；（注销时，仅需上送 device_id 字段）
        if (Objects.equals(operationId, "02")) {
            return request;
        }
        //商户信息
        final Map merchant = merchantService.getMerchantByMerchantSn(merchantSn);
        ListResult listResult = merchantBankService.findMerchantBankAccountPres(null,
                CollectionUtil.hashMap("merchant_id", BeanUtil.getPropString(merchant, DaoConstants.ID), "default_status", DEFAULT_TRUE));
        AtomicReference<Map> bankAccount = new AtomicReference();
        Optional.ofNullable(listResult)
                .filter(Objects::nonNull)
                .map(ListResult::getRecords)
                .filter(Objects::nonNull)
                .filter(records -> records.size() > 0)
                .ifPresent(records -> {
                    // records不为空并且size大于0,处理业务逻辑
                    bankAccount.set(listResult.getRecords().get(0));
                });
        //获取营业执照信息
        final String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        final Map<String, Object> license = merchantBusinessLicenseService.getBusinessLicenseByMerchantId(merchantId);
        //该名称是公司主体全称
        String merchantName = AliCommonUtil.convertMerchantName(merchant, bankAccount.get(), license);

        //终端布放地址
        final String deviceAddress = getTerminalAddress(storeSn, merchantSn);
        //产品定义取商户经营名称
        request.setMerchantName(merchantName);
        //默认上送11
        request.setTerminalType("11");
        //取值范围： 00：启用； 01：注销；注：终端注销时非必填
        request.setTerminalState("00");
        request.setTerminalAddress(deviceAddress);
        return request;

    }


    /**
     * 构建终端采集信息
     *
     * @param dto         绑定参数
     * @param operationId 本次操作标识，取值范围： 00：新增； 01：修改； 02：注销；（注销时，仅需上送 device_id 字段）
     * @return
     */
    public WxTermInfoRequest buildWXTermInfoRequest(BaseTermInfoDTO dto, String operationId) {
        //收单机构微信相关基础参数
        final WxTermInfoRequest request = new WxTermInfoRequest();
        request.setSubMchId(dto.getSubMchId());
        if (Objects.equals(operationId, "02")) {
            request.setOperationId(operationId);
            request.setDeviceId(dto.getDeviceId());
            return request;
        }
        String merchantSn = dto.getMerchantSn();
        String storeSn = dto.getStoreSn();
        if (StringUtils.isEmpty(merchantSn) || StringUtils.isEmpty(storeSn)) {
            throw new ContractBizException(String.format("商户号%s或者门店号%s不能为空", merchantSn, storeSn));
        }
        //商户信息
        final Map merchant = merchantService.getMerchantByMerchantSn(merchantSn);
        Map<String, Object> context = Maps.newHashMap();
        context.put("merchant", merchant);
        //获取营业执照信息
        final String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        final Map<String, Object> license = merchantBusinessLicenseService.getBusinessLicenseByMerchantId(merchantId);
        context.put("merchantBusinessLicense", license);
        //获取当前再用银行卡信息
        ListResult listResult = merchantBankService.findMerchantBankAccountPres(null,
                CollectionUtil.hashMap("merchant_id", BeanUtil.getPropString(merchant, com.wosai.upay.common.dao.DaoConstants.ID), "default_status", 1));

        Map bankAccount = null;
        if (listResult != null && listResult.getTotal() != 0) {
            bankAccount = listResult.getRecords().get(0);
        }
        context.put("bankAccount", bankAccount);
        final String merchantName = weiXinRuleBiz.getMerchantName(context);
        final String deviceAddress = getTerminalAddress(storeSn, merchantSn);
        // 本次操作标识，取值范围： 00：新增； 01：修改； 02：注销；（注销时，仅需上送 device_id 字段）
        request.setOperationId(operationId);
        request.setDeviceId(dto.getDeviceId());
        //产品定义取商户经营名称
        request.setMerchantName(merchantName);
        //默认上送11
        request.setDeviceType("11");
        //取值范围： 00：启用； 01：注销；注：终端注销时非必填
        request.setDeviceState("00");
        request.setDeviceAddress(deviceAddress);
        request.setMerchantRemark(System.currentTimeMillis() + "" + getRandomInteger(100000, 999999));
        return request;

    }

    public static Integer getRandomInteger(Integer min, Integer max) {
        return ThreadLocalRandom.current().nextInt(min, max + 1);
    }

    /**
     * 获取终端地址
     *
     * @param storeSn
     * @param merchantSn
     * @return
     */
    public String getTerminalAddress(String storeSn, String merchantSn) {
        List<String> addressList;
        if (StringUtils.isEmpty(storeSn)) {
            addressList = getShopStreetAddressByExtra(merchantSn);
        } else {
            final StoreInfo store = storeService.getStoreBySn(storeSn, null);
            addressList = Lists.newArrayList(store.getProvince(), store.getCity(), store.getDistrict(), getShopStreetAddressByExtra(store));
            if (addressList.contains(null) || addressList.stream().anyMatch(x -> StringUtils.isEmpty(x))) {
                addressList = getShopStreetAddressByExtra(merchantSn);
            }
        }
        List<String> finalAddressList = addressList;
        final List<String> collect = IntStream.range(0, addressList.size()).mapToObj(index -> {
            final String substring = Utils.substring(finalAddressList.get(index), 30);
            return substring;
        }).collect(Collectors.toList());
        return Joiner.on("-").skipNulls().join(collect).replaceAll("\\s", "");
    }

    /**
     * 获取华夏终端地址信息
     *
     * @param storeSn
     * @param merchantSn
     * @return
     */
    public Map<String, String> getHxTerminalAddress(String storeSn, String merchantSn) {
        List<String> addressList;
        if (StringUtils.isEmpty(storeSn)) {
            addressList = getShopStreetAddressByExtra(merchantSn);
        } else {
            final StoreInfo store = storeService.getStoreBySn(storeSn, null);
            addressList = Lists.newArrayList(store.getProvince(), store.getCity(), store.getDistrict(), getShopStreetAddressByExtra(store));
            if (addressList.contains(null) || addressList.stream().anyMatch(x -> StringUtils.isEmpty(x))) {
                //商户信息
                addressList = getShopStreetAddressByExtra(merchantSn);
            }
        }
        //端布放地址为门店地址，地址中必须有不少于6个汉字，数字字母不算，如果地址少于6个汉字的，默认加上省市区凑够6个汉字
        String streetAddress = fixStreetAddress(addressList.get(0), addressList.get(1), addressList.get(2), addressList.get(3).replaceAll("\\s", ""));

        //收钱吧系统内部的省市区
        Map info = CollectionUtil.hashMap(Merchant.PROVINCE, addressList.get(0),
                Merchant.CITY, addressList.get(1),
                Merchant.DISTRICT, addressList.get(2)
        );
        //按照华夏的省市区code转化了一下
        District districtCode = districtBiz.getDistrictCode(info, McConstant.ACQUIRER_HXB);
        Map<String, String> hxAddress = CollectionUtil.hashMap(
                PROVINCE_CODE, districtCode.getProvince_code(),
                CITY_CODE, districtCode.getCity_code(),
                DISTRICT_CODE, districtCode.getCode(),
                Merchant.STREET_ADDRESS, streetAddress
        );
        return hxAddress;
    }

    private static final Pattern HANZI_PATTERN = Pattern.compile("[\\u4e00-\\u9fa5]+");

    /**
     * 终端布放地址为门店地址，地址中必须有不少于6个汉字，数字字母不算，如果地址少于6个汉字的，默认加上省市区凑够6个汉字
     *
     * @param province
     * @param city
     * @param district
     * @param detailAddress
     * @return
     */
    public static String fixStreetAddress(String province, String city, String district, String detailAddress) {
        // 使用正则表达式匹配汉字
        Matcher matcher = HANZI_PATTERN.matcher(detailAddress);

        int count = 0;
        while (matcher.find()) {
            count += matcher.group().length();
        }

        StringBuilder sb = new StringBuilder(detailAddress);

        if (count < 6 && sb.length() < 30) {
            // 地址中的汉字数量少于6个，需要补足
            if (sb.length() + district.length() <= 30) {
                sb.insert(0, district);
                count += district.length();

                if (count < 6 && sb.length() + city.length() <= 30) {
                    sb.insert(0, city);
                    count += city.length();

                    if (count < 6 && sb.length() + province.length() <= 30) {
                        sb.insert(0, province);
                    }
                }
            }
        } else if (sb.length() > 30) {
            sb.setLength(30);
        }

        return sb.toString();
    }


    private String getShopStreetAddressByExtra(StoreInfo store) {
        OffsetUtil.OffsetDistrict offsetDistrict = OffsetUtil.getOffsetDistrict(store);
        return WosaiStringUtils.isNotEmpty(offsetDistrict.getStreet_address()) ?
                offsetDistrict.getStreet_address() : store.getStreet_address();
    }

    private List<String> getShopStreetAddressByExtra(String merchantSn) {
        Map merchant = merchantService.getMerchantByMerchantSn(merchantSn);
        return Lists.newArrayList(BeanUtil.getPropString(merchant, Merchant.PROVINCE),
                BeanUtil.getPropString(merchant, Merchant.CITY),
                BeanUtil.getPropString(merchant, Merchant.DISTRICT),
                getStreetAddressFromExtra(merchant));
    }


}
