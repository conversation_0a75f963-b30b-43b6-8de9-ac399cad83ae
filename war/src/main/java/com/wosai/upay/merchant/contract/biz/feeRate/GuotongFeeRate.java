package com.wosai.upay.merchant.contract.biz.feeRate;

import com.wosai.upay.job.Constants.TradeConstants;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/11/24
 */
public class GuotongFeeRate extends AcquirerFeeRate {
    private static final double MIN_RATE = 0.383;
    private static final double MAX_RATE = 0.6;
    private static final String DEFAULT_RATE = "0.6";

    public GuotongFeeRate(List<Map> payConfigs) {
        super(payConfigs);
    }

    /**
     * 如果上送的云闪付>1000费率不在 0.383-0.6之间的费率，则取固定0.6
     * 0.383-0.6 为合同约定
     *
     * @param rate 费率信息
     * @return 上送费率信息
     */
    private String validateAndAdjustRate(String rate) {
        if (rate == null) {
            return DEFAULT_RATE;
        }
        double rateValue = Double.parseDouble(rate);
        if (rateValue < MIN_RATE || rateValue > MAX_RATE) {
            return DEFAULT_RATE;
        }
        return rate;
    }

    @Override
    public GuotongFeeRateModel buildFeeRate() {
        GuotongFeeRateModel guotongFeeRateModel = new GuotongFeeRateModel();

        // 支付宝
        SqbFeeRate.PayWayFeeRate aliFeeRate = sqbFeeRate.getPayWayFeeRate(TradeConstants.PAYWAY_ALIPAY2);
        guotongFeeRateModel.setAli(aliFeeRate.getFixedFeeRate());

        // 微信
        SqbFeeRate.PayWayFeeRate wxFeeRate = sqbFeeRate.getPayWayFeeRate(TradeConstants.PAYWAY_WEIXIN);
        guotongFeeRateModel.setWx(wxFeeRate.getFixedFeeRate());

        SqbFeeRate.PayWayFeeRate unionFeeRate = sqbFeeRate.getPayWayFeeRate(TradeConstants.PAYWAY_UNIONPAY);
        if (unionFeeRate.isFixed()) {
            String fixedRate = unionFeeRate.getFixedFeeRate();
            guotongFeeRateModel.setRateYlCap0(fixedRate);
            guotongFeeRateModel.setRateYlCap1(validateAndAdjustRate(fixedRate));
            guotongFeeRateModel.setRateYlRat0(fixedRate);
            guotongFeeRateModel.setRateYlRat1(validateAndAdjustRate(fixedRate));
            return guotongFeeRateModel;
        }
        if (unionFeeRate.isChannelLadder()) {
            SqbFeeRate.PayWayFeeRate.ChannelLadderFeeRate channelLadderFeeRate = unionFeeRate.getChannelLadderFeeRate();
            for (SqbFeeRate.PayWayFeeRate.LadderFeeRate ladderFeeRate : channelLadderFeeRate.getDebit()) {
                if (ladderFeeRate.getMin() == 0.0 && ladderFeeRate.getMax() == 1000.0) {
                    guotongFeeRateModel.setRateYlCap0(ladderFeeRate.getFeeRate());
                }
                if (ladderFeeRate.getMin() == 1000.0) {
                    guotongFeeRateModel.setRateYlCap1(validateAndAdjustRate(ladderFeeRate.getFeeRate()));
                }
            }
            for (SqbFeeRate.PayWayFeeRate.LadderFeeRate ladderFeeRate : channelLadderFeeRate.getCredit()) {
                if (ladderFeeRate.getMin() == 0.0 && ladderFeeRate.getMax() == 1000.0) {
                    guotongFeeRateModel.setRateYlRat0(ladderFeeRate.getFeeRate());
                }
                if (ladderFeeRate.getMin() == 1000.0) {
                    guotongFeeRateModel.setRateYlRat1(validateAndAdjustRate(ladderFeeRate.getFeeRate()));
                }
            }
            return guotongFeeRateModel;
        }
        if (unionFeeRate.isChannel()) {
            SqbFeeRate.PayWayFeeRate.ChannelFeeRate channelFeeRate = unionFeeRate.getChannelFeeRate();
            guotongFeeRateModel.setRateYlCap0(channelFeeRate.getDebit());
            guotongFeeRateModel.setRateYlCap1(validateAndAdjustRate(channelFeeRate.getDebit()));
            guotongFeeRateModel.setRateYlRat0(channelFeeRate.getCredit());
            guotongFeeRateModel.setRateYlRat1(validateAndAdjustRate(channelFeeRate.getCredit()));
            return guotongFeeRateModel;
        }
        if (unionFeeRate.isLadder()) {
            for (SqbFeeRate.PayWayFeeRate.LadderFeeRate ladderFeeRate : unionFeeRate.getLadderFeeRate()) {
                if (ladderFeeRate.getMin() == 0.0 && ladderFeeRate.getMax() == 1000.0) {
                    guotongFeeRateModel.setRateYlCap0(ladderFeeRate.getFeeRate());
                    guotongFeeRateModel.setRateYlRat0(ladderFeeRate.getFeeRate());
                }
                if (ladderFeeRate.getMin() == 1000.0) {
                    guotongFeeRateModel.setRateYlCap1(validateAndAdjustRate(ladderFeeRate.getFeeRate()));
                    guotongFeeRateModel.setRateYlRat1(validateAndAdjustRate(ladderFeeRate.getFeeRate()));
                }
            }
            return guotongFeeRateModel;
        }
        throw new ContractBizException("不支持的费率类型");
    }

    @Data
    public static class GuotongFeeRateModel {
        /**
         * 支付宝费率
         */
        private String ali;
        /**
         * 微信费率
         */
        private String wx;
        /**
         * 银联扫码借记卡扣率小于1000费率
         * 单位%，支持小数点后三位
         */
        private String rateYlCap0;
        /**
         * 银联扫码借记卡扣率大于1000费率
         * 单位%，支持小数点后三位
         */
        private String rateYlCap1;
        /**
         * 银联扫码贷记卡扣率小于1000费率
         * 单位%，支持小数点后三位
         */
        private String rateYlRat0;
        /**
         * 银联扫码贷记卡扣率大于1000费率
         * 单位%，支持小数点后三位
         */
        private String rateYlRat1;
    }


}
