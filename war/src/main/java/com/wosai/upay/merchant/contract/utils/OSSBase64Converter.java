package com.wosai.upay.merchant.contract.utils;

import com.wosai.oss.OssUrlEncrypt;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64OutputStream;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.*;
import java.net.ConnectException;
import java.net.HttpURLConnection;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.util.Base64;

/**
 * OSS文件Base64转换工具
 *
 * <AUTHOR>
 * @date 2025/5/23 16:12
 */
@Component
@Slf4j
public class OSSBase64Converter {

    @Autowired
    private OssUrlEncrypt ossUrlEncrypt;

    /**
     * 将文件转化为base64字符串
     *
     * @param fileUrl 文件url
     * @return base64字符串
     */
    public String urlToBase64(String fileUrl) {
        if (StringUtils.isBlank(fileUrl)) {
            log.error("fileUrl is empty");
            return null;
        }

        HttpURLConnection connection = null;
        InputStream inputStream = null;
        ByteArrayOutputStream outputStream = null;
        File tempFile = null;

        try {
            baseUrl(fileUrl);
            String encryptedUrl = ossUrlEncrypt.encryptUrl(fileUrl);
            URL url = new URL(encryptedUrl);

            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000); // 5秒连接超时
            connection.setReadTimeout(30000);  // 30秒读取超时

            int responseCode = connection.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                log.error("Failed to connect to OSS, response code: {}", responseCode);
                return null;
            }

            inputStream = connection.getInputStream();

            int contentLength = connection.getContentLength();
            // 如果文件过大，使用临时文件处理
            if (contentLength > 5 * 1024 * 1024) { // 大于5MB的文件使用临时文件
                return handleLargeFile(inputStream, fileUrl);
            }

            // 对于小文件，直接在内存中处理
            outputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            byte[] fileBytes = outputStream.toByteArray();
            return Base64.getEncoder().encodeToString(fileBytes);

        } catch (Exception e) {
            log.error("Error converting OSS file to Base64: {}", fileUrl, e);
            return null;
        } finally {
            // 关闭资源
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("Error closing input stream", e);
                }
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("Error closing output stream", e);
                }
            }
            if (connection != null) {
                connection.disconnect();
            }
            // 删除临时文件
            if (tempFile != null && tempFile.exists()) {
                boolean deleted = tempFile.delete();
                if (!deleted) {
                    log.warn("Failed to delete temporary file: {}", tempFile.getAbsolutePath());
                    // 注册关闭JVM时删除文件
                    tempFile.deleteOnExit();
                }
            }
        }
    }

    private void baseUrl(String fileUrl) {
        if (org.apache.commons.lang3.StringUtils.isBlank(fileUrl)) {
            return;
        }
        if (fileUrl.contains("x-oss-process")) {
            fileUrl = fileUrl.substring(0, fileUrl.indexOf("x-oss-process") - 1);
        }
        if (fileUrl.contains("?")) {
            fileUrl = fileUrl.substring(0, fileUrl.lastIndexOf("?"));
        }
    }

    /**
     * 处理大文件，使用临时文件避免内存溢出
     *
     * @param inputStream 输入流
     * @param fileUrl 文件URL（用于日志）
     * @return Base64编码的字符串
     */
    private String handleLargeFile(InputStream inputStream, String fileUrl) throws IOException {
        File tempFile = null;
        FileOutputStream fileOutputStream = null;
        FileInputStream fileInputStream = null;
        ByteArrayOutputStream outputStream = null;

        try {
            // 创建临时文件
            tempFile = File.createTempFile("oss_", "_temp");
            fileOutputStream = new FileOutputStream(tempFile);

            // 将输入流写入临时文件
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                fileOutputStream.write(buffer, 0, bytesRead);
            }
            fileOutputStream.flush();
            fileOutputStream.close();
            fileOutputStream = null; // 避免在finally中再次关闭

            // 从临时文件读取并转换为Base64
            fileInputStream = new FileInputStream(tempFile);
            outputStream = new ByteArrayOutputStream();
            Base64OutputStream base64OutputStream = new Base64OutputStream(outputStream, true);

            buffer = new byte[8192];
            while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                base64OutputStream.write(buffer, 0, bytesRead);
            }
            base64OutputStream.flush();
            base64OutputStream.close();

            return outputStream.toString("UTF-8");

        } catch (IOException e) {
            log.error("Error handling large file: {}", fileUrl, e);
            throw e;
        } finally {
            // 关闭资源
            if (fileOutputStream != null) {
                try {
                    fileOutputStream.close();
                } catch (IOException e) {
                    log.error("Error closing file output stream", e);
                }
            }
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (IOException e) {
                    log.error("Error closing file input stream", e);
                }
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("Error closing output stream", e);
                }
            }
            // 删除临时文件
            if (tempFile != null && tempFile.exists()) {
                boolean deleted = tempFile.delete();
                if (!deleted) {
                    log.warn("Failed to delete temporary file: {}", tempFile.getAbsolutePath());
                    // 注册关闭JVM时删除文件
                    tempFile.deleteOnExit();
                }
            }
        }
    }
}
