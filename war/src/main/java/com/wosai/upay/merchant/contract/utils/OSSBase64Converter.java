package com.wosai.upay.merchant.contract.utils;

import com.wosai.oss.OssUrlEncrypt;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64OutputStream;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.*;
import java.net.ConnectException;
import java.net.HttpURLConnection;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.util.Base64;

/**
 * OSS文件Base64转换工具
 *
 * <AUTHOR>
 * @date 2025/5/23 16:12
 */
@Component
@Slf4j
public class OSSBase64Converter {

    @Autowired
    private OssUrlEncrypt ossUrlEncrypt;

    /**
     * 将文件转化为base64字符串
     *
     * @param fileUrl 文件url
     * @return base64字符串
     */
    public String urlToBase64(String fileUrl) {
        if (StringUtils.isBlank(fileUrl)) {
            log.error("fileUrl is empty");
            return null;
        }

        HttpURLConnection connection = null;
        InputStream inputStream = null;
        ByteArrayOutputStream outputStream = null;
        File tempFile = null;

        try {
            baseUrl(fileUrl);
            String encryptedUrl = ossUrlEncrypt.encryptUrl(fileUrl);
            URL url = new URL(encryptedUrl);

            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000); // 5秒连接超时
            connection.setReadTimeout(60000);  // 60秒读取超时

            int responseCode = connection.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                String errorMsg = String.format("OSS文件访问失败，HTTP状态码: %d，URL: %s", responseCode, fileUrl);
                log.error(errorMsg);
                throw new ContractBizException("文件访问失败");
            }
            inputStream = connection.getInputStream();
            int contentLength = connection.getContentLength();
            log.info("OSS文件大小: {} bytes, URL: {}", contentLength > 0 ? contentLength : "未知", fileUrl);
            if (contentLength > 100 * 1024 * 1024) {
                String errorMsg = String.format("文件过大，超过100MB限制。文件大小: %s", formatFileSize(contentLength));
                log.error(errorMsg);
                throw new ContractBizException("文件过大超过限制");
            }

            // 如果文件过大，使用临时文件处理
            if (contentLength > 5 * 1024 * 1024) { // 大于5MB的文件使用临时文件
                log.info("文件大小超过5MB，使用临时文件处理: {}", fileUrl);
                return handleLargeFile(inputStream, fileUrl);
            }

            // 对于小文件，直接在内存中处理
            outputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            byte[] fileBytes = outputStream.toByteArray();
            return Base64.getEncoder().encodeToString(fileBytes);

        } catch (SocketTimeoutException e) {
            log.error("OSS文件读取超时: {}, 超时时间: 连接5秒/读取60秒", fileUrl, e);
            throw new ContractBizException("OSS文件读取超时", e);
        } catch (ConnectException e) {
            log.error("OSS文件连接失败: {}", fileUrl, e);
            throw new ContractBizException("无法连接到OSS服务器", e);
        } catch (IOException e) {
            log.error("OSS文件读取IO异常: {}", fileUrl, e);
            throw new ContractBizException("OSS文件读取失败", e);
        } catch (Exception e) {
            log.error("OSS文件转换Base64异常: {}", fileUrl, e);
            throw new ContractBizException("OSS文件转换为Base64编码失败: " + e.getMessage(), e);
        } finally {
            // 关闭资源
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("Error closing input stream", e);
                }
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("Error closing output stream", e);
                }
            }
            if (connection != null) {
                connection.disconnect();
            }
            // 删除临时文件
            if (tempFile != null && tempFile.exists()) {
                boolean deleted = tempFile.delete();
                if (!deleted) {
                    log.warn("Failed to delete temporary file: {}", tempFile.getAbsolutePath());
                    // 注册关闭JVM时删除文件
                    tempFile.deleteOnExit();
                }
            }
        }
    }

    private void baseUrl(String fileUrl) {
        if (org.apache.commons.lang3.StringUtils.isBlank(fileUrl)) {
            return;
        }
        if (fileUrl.contains("x-oss-process")) {
            fileUrl = fileUrl.substring(0, fileUrl.indexOf("x-oss-process") - 1);
        }
        if (fileUrl.contains("?")) {
            fileUrl = fileUrl.substring(0, fileUrl.lastIndexOf("?"));
        }
    }

    /**
     * 处理大文件，使用临时文件避免内存溢出
     *
     * @param inputStream 输入流
     * @param fileUrl 文件URL（用于日志）
     * @return Base64编码的字符串
     */
    private String handleLargeFile(InputStream inputStream, String fileUrl) throws IOException {
        File tempFile = null;
        FileOutputStream fileOutputStream = null;
        FileInputStream fileInputStream = null;
        ByteArrayOutputStream outputStream = null;

        try {
            // 创建临时文件
            tempFile = File.createTempFile("oss_", "_temp");
            fileOutputStream = new FileOutputStream(tempFile);

            // 将输入流写入临时文件
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                fileOutputStream.write(buffer, 0, bytesRead);
            }
            fileOutputStream.flush();
            fileOutputStream.close();
            fileOutputStream = null; // 避免在finally中再次关闭

            // 从临时文件读取并转换为Base64
            fileInputStream = new FileInputStream(tempFile);
            outputStream = new ByteArrayOutputStream();
            Base64OutputStream base64OutputStream = new Base64OutputStream(outputStream, true);

            buffer = new byte[8192];
            while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                base64OutputStream.write(buffer, 0, bytesRead);
            }
            base64OutputStream.flush();
            base64OutputStream.close();

            return outputStream.toString("UTF-8");

        } catch (SocketTimeoutException e) {
            log.error("大文件读取超时: {}, 超时时间: 读取30秒", fileUrl, e);
            throw new IOException("大文件读取超时，请检查网络连接。读取超时: 30秒", e);
        } catch (IOException e) {
            log.error("大文件处理IO异常: {}", fileUrl, e);
            throw new IOException("大文件处理失败: " + e.getMessage(), e);
        } finally {
            // 关闭资源
            if (fileOutputStream != null) {
                try {
                    fileOutputStream.close();
                } catch (IOException e) {
                    log.error("Error closing file output stream", e);
                }
            }
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (IOException e) {
                    log.error("Error closing file input stream", e);
                }
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("Error closing output stream", e);
                }
            }
            // 删除临时文件
            if (tempFile != null && tempFile.exists()) {
                boolean deleted = tempFile.delete();
                if (!deleted) {
                    log.warn("Failed to delete temporary file: {}", tempFile.getAbsolutePath());
                    // 注册关闭JVM时删除文件
                    tempFile.deleteOnExit();
                }
            }
        }
    }

    /**
     * 格式化文件大小为可读格式
     *
     * @param bytes 文件大小（字节）
     * @return 格式化后的文件大小字符串
     */
    private String formatFileSize(long bytes) {
        if (bytes < 0) {
            return "未知";
        }

        String[] units = {"B", "KB", "MB", "GB"};
        int unitIndex = 0;
        double size = bytes;

        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }

        return String.format("%.2f %s", size, units[unitIndex]);
    }
}
