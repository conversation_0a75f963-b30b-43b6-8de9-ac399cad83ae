package com.wosai.upay.merchant.contract.utils;

import com.wosai.mpay.util.StringUtils;
import com.wosai.oss.OssUrlEncrypt;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64OutputStream;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Base64;

/**
 * OSS文件Base64转换工具
 *
 * <AUTHOR>
 * @date 2025/5/23 16:12
 */
@Component
@Slf4j
public class OSSBase64Converter {

    @Autowired
    private OssUrlEncrypt ossUrlEncrypt;

    /**
     * 将文件转化为base64字符串
     *
     * @param fileUrl 文件url
     * @return base64字符串
     */
    public String urlToBase64(String fileUrl) {
        if (StringUtils.isEmpty(fileUrl)) {
            log.error("fileUrl is empty");
            return null;
        }

        HttpURLConnection connection = null;
        InputStream inputStream = null;
        ByteArrayOutputStream outputStream = null;
        File tempFile = null;

        try {
            // 使用ossUrlEncrypt.encryptUrl获取可访问URL
            String encryptedUrl = ossUrlEncrypt.encryptUrl(fileUrl);
            URL url = new URL(encryptedUrl);

            // 建立连接
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000); // 5秒连接超时
            connection.setReadTimeout(30000);  // 30秒读取超时

            // 获取连接响应码
            int responseCode = connection.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                log.error("Failed to connect to OSS, response code: {}", responseCode);
                return null;
            }

            // 获取输入流
            inputStream = connection.getInputStream();

            // 判断文件大小
            int contentLength = connection.getContentLength();
            log.info("File size: {} bytes", contentLength);

            // 如果文件过大，使用临时文件处理
            if (contentLength > 5 * 1024 * 1024) { // 大于5MB的文件使用临时文件
                return handleLargeFile(inputStream, fileUrl);
            }

            // 对于小文件，直接在内存中处理
            outputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            // 转换为Base64
            byte[] fileBytes = outputStream.toByteArray();
            return Base64.getEncoder().encodeToString(fileBytes);

        } catch (Exception e) {
            log.error("Error converting OSS file to Base64: {}", fileUrl, e);
            return null;
        } finally {
            // 关闭资源
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("Error closing input stream", e);
                }
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("Error closing output stream", e);
                }
            }
            if (connection != null) {
                connection.disconnect();
            }
            // 删除临时文件
            if (tempFile != null && tempFile.exists()) {
                boolean deleted = tempFile.delete();
                if (!deleted) {
                    log.warn("Failed to delete temporary file: {}", tempFile.getAbsolutePath());
                    // 注册关闭JVM时删除文件
                    tempFile.deleteOnExit();
                }
            }
        }
    }

    /**
     * 处理大文件，使用临时文件避免内存溢出
     *
     * @param inputStream 输入流
     * @param fileUrl 文件URL（用于日志）
     * @return Base64编码的字符串
     */
    private String handleLargeFile(InputStream inputStream, String fileUrl) throws IOException {
        File tempFile = null;
        FileOutputStream fileOutputStream = null;
        FileInputStream fileInputStream = null;
        ByteArrayOutputStream outputStream = null;

        try {
            // 创建临时文件
            tempFile = File.createTempFile("oss_", "_temp");
            fileOutputStream = new FileOutputStream(tempFile);

            // 将输入流写入临时文件
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                fileOutputStream.write(buffer, 0, bytesRead);
            }
            fileOutputStream.flush();
            fileOutputStream.close();
            fileOutputStream = null; // 避免在finally中再次关闭

            // 从临时文件读取并转换为Base64
            fileInputStream = new FileInputStream(tempFile);
            outputStream = new ByteArrayOutputStream();
            Base64OutputStream base64OutputStream = new Base64OutputStream(outputStream, true);

            buffer = new byte[8192];
            while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                base64OutputStream.write(buffer, 0, bytesRead);
            }
            base64OutputStream.flush();
            base64OutputStream.close();

            return outputStream.toString("UTF-8");

        } catch (IOException e) {
            log.error("Error handling large file: {}", fileUrl, e);
            throw e;
        } finally {
            // 关闭资源
            if (fileOutputStream != null) {
                try {
                    fileOutputStream.close();
                } catch (IOException e) {
                    log.error("Error closing file output stream", e);
                }
            }
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (IOException e) {
                    log.error("Error closing file input stream", e);
                }
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("Error closing output stream", e);
                }
            }
            // 删除临时文件
            if (tempFile != null && tempFile.exists()) {
                boolean deleted = tempFile.delete();
                if (!deleted) {
                    log.warn("Failed to delete temporary file: {}", tempFile.getAbsolutePath());
                    // 注册关闭JVM时删除文件
                    tempFile.deleteOnExit();
                }
            }
        }
    }
}
