package com.wosai.upay.merchant.contract.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.upay.merchant.contract.biz.payway.AliPayBiz;
import com.wosai.upay.merchant.contract.model.dto.AliMerchantBasicInfoRspDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 支付宝服务
 *
 * <AUTHOR>
 * @date 2024/3/25 10:49
 */
@Service
@AutoJsonRpcServiceImpl
public class AliPayServiceImpl implements AliPayService {

    @Resource
    private AliPayBiz aliPayBiz;

    /**
     * 根据报备参数id,获取支付宝商户信息
     * 后续新增收单机构需要维护该方法
     * 目前支持的provider:
     * 1016     lklV3       银联的接口查询
     * 1033     lklV3       银联的接口查询
     * 1037     haike       haike的接口查询
     * 1020     tonglian    tonglian的接口查询 通联不支持 废弃
     * 1035     tonglianV2  tonglianV2的接口查询 返回信息太少,移除
     * 1038     fuyou       fuyou接口查询
     *
     * @param merchantProviderParamsId 商户报备参数ID
     * @return 支付宝商户信息
     */
    @Override
    public AliMerchantBasicInfoRspDTO getLklV3AliPayMerchantInfo(String merchantProviderParamsId) {
        return aliPayBiz.getAliPayMerchantInfo(merchantProviderParamsId).orElse(null);
    }
}
