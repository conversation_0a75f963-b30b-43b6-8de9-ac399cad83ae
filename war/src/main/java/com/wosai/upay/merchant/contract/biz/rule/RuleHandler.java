package com.wosai.upay.merchant.contract.biz.rule;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-08-24
 */
public interface RuleHandler {

    /**
     * 获取排序值
     *
     * @return
     */
    int getOrder();

    /**
     * 获取支持的rule
     *
     * @return
     */
    String supportRule();

    /**
     * 应用规则
     *
     * @param source     源数据
     * @param value      处理前的值
     * @param ruleDetail 规则具体配置
     * @return
     */
    Object apply(Map<String, Object> source, Object value, Object ruleDetail);
}
