package com.wosai.upay.merchant.contract.utils;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.mc.model.StoreInfo;
import com.wosai.upay.core.model.Merchant;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.MapUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/6/29
 */
public class OffsetUtil {

    /**
     *
     * @param info merchant 或者 store
     * @return
     */
    public static OffsetDistrict getOffsetDistrict(Map info) {
        Map extra = WosaiMapUtils.getMap(info, Merchant.EXTRA);
        Map offsetInfo = JSONObject.parseObject(BeanUtil.getPropString(extra, "offsetInfo"));


        return new OffsetDistrict()
                .setProvince(BeanUtil.getPropString(offsetInfo, Merchant.PROVINCE, BeanUtil.getPropString(info, Merchant.PROVINCE)))
                .setCity(BeanUtil.getPropString(offsetInfo, Merchant.CITY, BeanUtil.getPropString(info, Merchant.CITY)))
                .setDistrict(BeanUtil.getPropString(offsetInfo, Merchant.DISTRICT, BeanUtil.getPropString(info, Merchant.DISTRICT)))
                .setStreet_address(BeanUtil.getPropString(offsetInfo, Merchant.STREET_ADDRESS, BeanUtil.getPropString(info, Merchant.STREET_ADDRESS)))
                .setLongitude(BeanUtil.getPropString(offsetInfo, Merchant.LONGITUDE, BeanUtil.getPropString(info, Merchant.LONGITUDE)))
                .setLatitude(BeanUtil.getPropString(offsetInfo, Merchant.LATITUDE, BeanUtil.getPropString(info, Merchant.LATITUDE)))
                ;
    }

    public static OffsetDistrict getOffsetDistrict(StoreInfo info) {
        Map extra = info.getExtra();
        Map offsetInfo = JSONObject.parseObject(BeanUtil.getPropString(extra, "offsetInfo"));

        return new OffsetDistrict()
                .setProvince(BeanUtil.getPropString(offsetInfo, Merchant.PROVINCE, BeanUtil.getPropString(info, Merchant.PROVINCE)))
                .setCity(BeanUtil.getPropString(offsetInfo, Merchant.CITY, BeanUtil.getPropString(info, Merchant.CITY)))
                .setDistrict(BeanUtil.getPropString(offsetInfo, Merchant.DISTRICT, BeanUtil.getPropString(info, Merchant.DISTRICT)))
                .setStreet_address(BeanUtil.getPropString(offsetInfo, Merchant.STREET_ADDRESS, BeanUtil.getPropString(info, Merchant.STREET_ADDRESS)))
                .setLongitude(BeanUtil.getPropString(offsetInfo, Merchant.LONGITUDE, BeanUtil.getPropString(info, Merchant.LONGITUDE)))
                .setLatitude(BeanUtil.getPropString(offsetInfo, Merchant.LATITUDE, BeanUtil.getPropString(info, Merchant.LATITUDE)))
                ;
    }


    /**
     * 根据商户信息获取电话
     *
     * @param info merchant 或者 store
     * @return 偏移后的电话信息
     */
    public static OffsetPhone getOffsetPhone(Map info) {
        Map extra = WosaiMapUtils.getMap(info, Merchant.EXTRA);
        Map offsetInfo = JSONObject.parseObject(BeanUtil.getPropString(extra, "offsetInfo"));


        return new OffsetPhone()
                .setContact_cellphone(BeanUtil.getPropString(offsetInfo, Merchant.CONTACT_CELLPHONE, BeanUtil.getPropString(info, Merchant.CONTACT_CELLPHONE)))
                .setCustomer_phone(BeanUtil.getPropString(offsetInfo, Merchant.CUSTOMER_PHONE, BeanUtil.getPropString(info, Merchant.CUSTOMER_PHONE)));
    }

    public static OffsetPhone getOffsetPhone(StoreInfo info) {
        Map extra = info.getExtra();
        Map offsetInfo = JSONObject.parseObject(BeanUtil.getPropString(extra, "offsetInfo"));

        return new OffsetPhone()
                .setContact_cellphone(BeanUtil.getPropString(offsetInfo, Merchant.CONTACT_CELLPHONE, info.getContact_cellphone()))
                .setCustomer_phone(BeanUtil.getPropString(offsetInfo, Merchant.CUSTOMER_PHONE, BeanUtil.getPropString(info, Merchant.CUSTOMER_PHONE)))
                ;
    }


    @Data
    @Accessors(chain = true)
    public static class OffsetDistrict {

        private String province;

        private String city;

        private String district;

        private String street_address;

        // 经度
        private String longitude;

        // 纬度
        private String latitude;

    }

    @Data
    @Accessors(chain = true)
    public static class OffsetPhone {

        // 联系电话
        private String contact_cellphone;

        // 客服电话
        private String customer_phone;

    }
}
