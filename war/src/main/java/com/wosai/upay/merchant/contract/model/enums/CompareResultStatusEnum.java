package com.wosai.upay.merchant.contract.model.enums;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 比较结果状态枚举
 *
 * <AUTHOR>
 */
public enum CompareResultStatusEnum implements ITextValueEnum<Integer> {

    EQUAL(0, "相等"),

    NOT_EQUAL(1, "不相等");


    private final Integer value;
    private final String text;

    CompareResultStatusEnum(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
