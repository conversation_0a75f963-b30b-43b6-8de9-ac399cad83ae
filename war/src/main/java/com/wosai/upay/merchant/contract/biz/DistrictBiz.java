package com.wosai.upay.merchant.contract.biz;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.mpay.util.MapUtils;
import com.wosai.mpay.util.StringUtils;
import com.wosai.upay.bank.info.api.model.District;
import com.wosai.upay.bank.info.api.service.DistrictsServiceV2;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.util.StringUtil;

import javax.annotation.PostConstruct;
import javax.validation.constraints.NotNull;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Component
public class DistrictBiz {

    @Autowired
    DistrictsServiceV2 districtsServiceV2;

    @ApolloJsonValue("${district_reflection}")
    private static Map<String, Map<String, String>> reflectionMap;

    public District getDistrictCode(Map<String, Object> merchant, String acquirer) {
        String province = BeanUtil.getPropString(merchant, Merchant.PROVINCE);
        String city = BeanUtil.getPropString(merchant, Merchant.CITY);
        String district = BeanUtil.getPropString(merchant, Merchant.DISTRICT);
        if (WosaiStringUtils.isEmptyAny(province, city, district)) {
            throw new ContractBizException("省市区信息不完整");
        }
        return getDistrictCode(province, city, district, acquirer);
    }

    public District getDistrictCode(String province, String city, String district, String acquirer) {
        District addressInfo = districtsServiceV2.getCodeByName(province + " " + city + " " + district);
        Optional.ofNullable(addressInfo).orElseThrow(() -> new ContractBizException("省市区不存在"));
        return packageDistrictBySqbRule(addressInfo, acquirer);
    }

    /**
     * 地区映射关系包装
     * 此处只能处理 以 province city area 单一维度为处理参数的包装情况
     *
     * @param district
     * @param acquirer
     * @return
     */
    private District packageDistrictBySqbRule(District district, String acquirer) {
        Map <String, String> reflection = MapUtils.getMap(reflectionMap, acquirer);
        if (MapUtils.isEmpty(reflection)){
            return district;
        }
        String reflectionCode = MapUtils.getString(reflection, district.getCode());
        if (StringUtils.isEmpty(reflectionCode)){
            return district;
        }
        String[] split = reflectionCode.split(",");
        district.setProvince_code(split[0]).setCity_code(split[1]).setCode(split[2]);
        return district;
    }

    public Triple<String, String, String> getDistrictCode(String districtCode, String acquirer) {
        if (WosaiStringUtils.isEmpty(districtCode)) {
            return ImmutableTriple.nullTriple();
        }
        Map<String, String> reflection = MapUtils.getMap(reflectionMap, acquirer);
        if (MapUtils.isEmpty(reflection)){
            return ImmutableTriple.of(districtCode.substring(0, 2) + "0000", districtCode.substring(0, 4) + "00", districtCode);
        }
        String reflectionCode = MapUtils.getString(reflection, districtCode);
        if (StringUtils.isEmpty(reflectionCode)){
            return ImmutableTriple.of(districtCode.substring(0, 2) + "0000", districtCode.substring(0, 4) + "00", districtCode);
        }
        String[] split = reflectionCode.split(",");
        return ImmutableTriple.of(split[0], split[1], split[2]);

    }

}
