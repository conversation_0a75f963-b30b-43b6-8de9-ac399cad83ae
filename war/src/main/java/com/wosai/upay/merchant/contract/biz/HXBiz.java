package com.wosai.upay.merchant.contract.biz;

import avro.shaded.com.google.common.collect.Maps;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractSubTaskAndParam;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import com.wosai.upay.job.service.callback.HxbCallBackService;
import com.wosai.upay.merchant.contract.config.ApolloParamsConfig;
import com.wosai.upay.merchant.contract.constant.Constant;
import com.wosai.upay.merchant.contract.constant.hx.HXConstant;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.exception.ContractException;
import com.wosai.upay.merchant.contract.exception.ContractSysException;
import com.wosai.upay.merchant.contract.model.ApolloConfigParams;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.MerchantProviderParams;
import com.wosai.upay.merchant.contract.model.huaxia.request.*;
import com.wosai.upay.merchant.contract.model.huaxia.response.*;
import com.wosai.upay.merchant.contract.model.provider.HXParam;
import com.wosai.upay.merchant.contract.model.terminal.AddTermInfoDTO;
import com.wosai.upay.merchant.contract.model.terminal.BaseTermInfoDTO;
import com.wosai.upay.merchant.contract.repository.ProviderTerminalRepository;
import com.wosai.upay.merchant.contract.service.BatchServiceWrapper;
import com.wosai.upay.merchant.contract.service.ProviderTradeParamsService;
import com.wosai.upay.merchant.contract.utils.SnowFlakeIdGenerator;
import com.wosai.upay.merchant.contract.utils.StringFilter;
import com.wosai.upay.merchant.contract.utils.hx.HXHttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Base64Utils;

import java.io.File;
import java.io.FileNotFoundException;
import java.net.MalformedURLException;
import java.util.*;

import static com.wosai.upay.merchant.contract.constant.Constant.*;
import static com.wosai.upay.merchant.contract.constant.ContractSubTaskTaskType.SUB_TASK_TASK_TYPE_CONTRACT;
import static com.wosai.upay.merchant.contract.constant.LakalaWanmaBusinessFileds.*;
import static com.wosai.upay.merchant.contract.constant.TradeParamConstant.*;
import static com.wosai.upay.merchant.contract.constant.hx.HXConstant.*;
import static com.wosai.upay.merchant.contract.converter.hx.HXConverter.hxOtherPayWayToProviderParams;
import static com.wosai.upay.merchant.contract.converter.hx.HXConverter.hxToProviderParams;
import static com.wosai.upay.merchant.contract.model.ApolloConfigParams.HX_BRANCH_CITY;
import static com.wosai.upay.merchant.contract.model.ApolloConfigParams.HX_ORGNO_APPID;
import static com.wosai.upay.merchant.contract.model.MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL;
import static com.wosai.upay.merchant.contract.model.MerchantProviderParams.PARAMS_CONFIG_STATUS_PRE;


/**
 * @Description:
 * <AUTHOR>
 * @Date: 2021/11/1 9:29 上午
 */
@Component
@Slf4j
public class HXBiz {

    @Value("${hx.url}")
    private String requestUrl;
    @Autowired
    private PicBiz picBiz;
    @Autowired
    private ProviderTradeParamsService providerTradeParamsService;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private HxbCallBackService hxbCallBackService;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private ApolloParamsConfig apolloParamsConfig;
    @Autowired
    Dao<Map<String, Object>> merchantProviderParamsDao;
    @Autowired
    Dao<Map<String, Object>> contractSubTaskDao;
    @Autowired
    private ProviderTerminalRepository terminalRepository;
    @Autowired
    private ProviderTerminalBiz providerTerminalBiz;
    @Autowired
    private ProviderTerminalIdBiz providerTerminalIdBiz;
    //成功
    private static final String success = "3";
    //驳回
    private static final String FAIL = "5";

    //成功
    public static final String QM50 = "2023071100005711";
    public static final String VERSION = "version";
    public static final String VERSION_NO = "2.0";
    @Autowired
    private BatchServiceWrapper batchServiceWrapper;

    private static ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    Dao<Map<String, Object>> providerTerminalDao;

    @Autowired
    TerminalService terminalService;
    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    @Value("${product.env}")
    private String env;
    @Autowired
    private ContractWith259Biz contractWith259Biz;

    /**
     * 商户新增
     */
    public HXBaseResponse merchantAdd(HXMerchantAdd merchantAdd, HXParam hxParam) {
        //参数组
        String content = JSONObject.toJSONString(merchantAdd);
        log.info("华夏商户进件请求参数:{}", content);
        //根据serverOrgNo获取appId
        final String hxAppId = getHxAppIdByServerOrgNo(merchantAdd.getServerOrgNo());
        //调用请求接口
        String send = HXHttpUtils.send(content, hxParam, MERCHANT_ADD, requestUrl, hxAppId);
        return JSON.parseObject(send, HXBaseResponse.class);
    }

    /**
     * 进件查询
     *
     * @param request
     * @param hxParam
     * @param hxAppId
     * @return
     */
    public HXMerchantQueryResponse queryApplyStatus(HXMerchantQuery request, HXParam hxParam, String hxAppId) {
        //参数组
        String content = JSONObject.toJSONString(request);
        log.info("华夏进件查询请求参数:{}", content);

        //调用请求接口
        String send = HXHttpUtils.send(content, hxParam, MERCHANT_QUERY, requestUrl, hxAppId);
        return JSON.parseObject(send, HXMerchantQueryResponse.class);
    }


    /**
     * 进件查询
     *
     * @param request
     * @param hxParam
     * @return
     */
    public HXMerchantInfoQueryResponse queryMerchantInfo(HXMerchantInfoQuery request, HXParam hxParam) {
        //参数组
        String content = JSONObject.toJSONString(request);
        log.info("华夏进件商户信息查询请求参数:{}", content);

        //调用请求接口
        String send = HXHttpUtils.send(content, hxParam, QUERY_MERCHANT_INFO, requestUrl, getHxAppIdByServerOrgNo(null));
        return JSON.parseObject(send, HXMerchantInfoQueryResponse.class);
    }



    /**
     * 新增支付目录
     *
     * @param subPayPath
     * @param hxParam
     * @return
     */
    public HXWechatSubAddResponse wechatAddPayPath(HXWechatSubPayPath subPayPath, HXParam hxParam, String hxAppId) {
        String content = JSONObject.toJSONString(subPayPath);
        log.info("华夏配置微信支付目录请求参数:{}", content);
        //调用请求接口
        String send = HXHttpUtils.send(content, hxParam, APP_PATH, requestUrl, hxAppId);
        return JSON.parseObject(send, HXWechatSubAddResponse.class);

    }

    /**
     * 新增支付目录
     *
     * @param subAppID
     * @param hxParam
     * @return
     */
    public HXWechatSubAddResponse wechatAddAppId(HXWechatSubAppID subAppID, HXParam hxParam, String hxAppId) {
        String content = JSONObject.toJSONString(subAppID);
        log.info("华夏配置微信APPID请求参数:{}", content);
        //调用请求接口
        String send = HXHttpUtils.send(content, hxParam, APP_APP_ID, requestUrl, hxAppId);
        return JSON.parseObject(send, HXWechatSubAddResponse.class);
    }


    /**
     * 其他支付类型进件 即新增providerParams参数
     *
     * @param contractSubTask
     * @param hxParam
     * @return
     */
    public ContractResponse contractOtherPayWay(ContractSubTask contractSubTask, HXParam hxParam) {
        ContractResponse response = new ContractResponse();
        try {
            //查询
            Integer payWay = contractSubTask.getPayway();
            Map providerParamsCon = providerTradeParamsService.getProviderParamsByRuleGroup(contractSubTask.getMerchant_sn(), contractSubTask.getRule_group_id(), 0);
            Map extra = WosaiMapUtils.getMap(providerParamsCon, MerchantProviderParams.EXTRA);
            Map<String, Object> paramsExtra = (Map<String, Object>) extra.get(String.valueOf(payWay));
            //组装
            MerchantProviderParamsDto providerParams = hxOtherPayWayToProviderParams(contractSubTask, hxParam, paramsExtra);
            providerParams.setPayway(payWay);
            providerParams.setParams_config_status(PARAMS_CONFIG_STATUS_NULL);
            if (payWay == MerchantProviderParams.PAYWAY_ALIPAY2) {
                //新增子商户号绑定终端任务
                terminalRepository.addBoundTerminalTask(contractSubTask.getMerchant_sn(), (String) paramsExtra.get(MerchantProviderParams.PAY_MERCHANT_ID), Integer.parseInt(hxParam.getProvider()), MerchantProviderParams.PAYWAY_ALIPAY2);
            } else if (payWay == MerchantProviderParams.PAYWAY_WEIXIN) {
                providerParams.setParams_config_status(PARAMS_CONFIG_STATUS_PRE);
                //新增子商户号绑定终端任务
                terminalRepository.addBoundTerminalTask(contractSubTask.getMerchant_sn(), (String) paramsExtra.get(MerchantProviderParams.PAY_MERCHANT_ID), Integer.parseInt(hxParam.getProvider()), MerchantProviderParams.PAYWAY_WEIXIN);
            } else if (payWay == MerchantProviderParams.PAYWAY_LKL_UNIONPAY) {
                //新增子商户号绑定终端任务
                terminalRepository.addBoundTerminalTask(contractSubTask.getMerchant_sn(), (String) paramsExtra.get(MerchantProviderParams.PAY_MERCHANT_ID), Integer.parseInt(hxParam.getProvider()), MerchantProviderParams.PAYWAY_LKL_UNIONPAY);
            }
            //保存
            Map merchantProviderParams = providerTradeParamsService.saveMerchantProviderParams(providerParams);
            String paramsId = WosaiMapUtils.getString(merchantProviderParams, DaoConstants.ID);
            //设置返回值
            response.setMerchantProviderParamsId(paramsId);
            response.setCode(200);
            response.setMessage("华夏银行进件成功");
            response.setTradeParam(paramsExtra);
            batchServiceWrapper.fillPayAuthInfo(paramsId);
        } catch (Exception e) {
            response.setCode(500);
            response.setMessage("华夏银行进件系统异常");
            log.error("华夏银行进件 商户{},支付方式{} 系统异常", contractSubTask.getMerchant_sn(), contractSubTask.getPayway(), e);
        }
        return response;
    }

    /**
     * 上传照片
     *
     * @param url         照片
     * @param serverOrgNo
     * @return 批次号
     */
    public void uploadPic(String url, String type, String id, HXParam hxParam, String serverOrgNo) {
        File file = null;
        try {
            file = picBiz.urlToFile(url, 1);
            //图片格式转换将jpeg转成jpg
            final String name = file.getName();
            String suffix = name.substring(name.lastIndexOf("."));
            if (suffix.equalsIgnoreCase(".jpeg")) {
                final String parent = file.getParent();
                final String newName = name.replace(".jpeg", ".jpg");
                file.renameTo(new File(parent + "/" + newName));
                file = new File(parent + "/" + newName);
            }
            byte[] fileByte = FileUtils.readFileToByteArray(file);
            String encode = Base64Utils.encodeToString(fileByte);
            String picBase64 = String.format("data:image/%s;base64,%s", file.getName().substring(file.getName().lastIndexOf(".") + 1), encode);
            //参数组装
            HXFile hxFile = new HXFile(id, type, picBase64);
            String content = JSONObject.toJSONString(hxFile);
            log.info("华夏银行图片上传请求参数:{}", content);
            //调用请求接口
            final String hxAppId = getHxAppIdByServerOrgNo(serverOrgNo);
            String send = HXHttpUtils.send(content, hxParam, FILE_UPLOAD, requestUrl, hxAppId);
            HXBaseResponse response = JSON.parseObject(send, HXBaseResponse.class);
            if (!SUCCESS.equals(response.getRespCode())) {
                throw new ContractBizException("华夏银行商上传图片异常" + response.getRespMsg());
            }
        } catch (FileNotFoundException | MalformedURLException | ContractException e) {
            log.error("华夏银行上传图片业务异常:", e);
            throw new ContractBizException("华夏银行上传图片业务异常:" + e.getMessage());
        } catch (Exception e) {
            log.error("华夏银行图片系统异常", e);
            throw new RuntimeException("华夏银行上传图片系统异常");
        } finally {
            //删除临时文件
            FileUtils.deleteQuietly(file);
        }

    }


    /**
     * 根据商户省市区 映射服务商编码
     *
     * @param province 省
     * @param city     市
     * @param district 区
     * @return
     */
    public String getOrgNo(String province, String city, String district) {
        //根据商户所在地 映射出银联地区code

        List<String> upDistrictCode = commonBiz.getUpDistrictCode(province, city, district);
        String countyCode = upDistrictCode.get(1);
        Map<String, String> map = apolloParamsConfig.getMap(HX_BRANCH_CITY, null);
        String orgNo = "";
        if (StringUtils.isNotBlank(countyCode)) {
            orgNo = map.get(countyCode);
        }
        return orgNo;
    }

    /**
     * 查询微信配置
     *
     * @param request
     * @param hxParam
     * @param hxAppId
     * @return
     */
    public HXWechatSubQueryResponse wechatConfigQuery(HXWechatSubQuery request, HXParam hxParam, String hxAppId) {
        String content = JSONObject.toJSONString(request);
        log.info("华夏查询微信配置请求参数：{}", content);
        String send = HXHttpUtils.send(content, hxParam, WECHAT_QUERY, requestUrl, hxAppId);
        return JSON.parseObject(send, HXWechatSubQueryResponse.class);
    }

    /**
     * 商户回调接口
     *
     * @param response
     */
    public void merchantContractCallBack(HXMerchantQueryResponse response) {
        if (StringUtils.isBlank(response.getId())) {
            throw new ContractBizException("华夏商户状态回调,进件ID不能为空");
        }
        ContractSubTaskAndParam contract = hxbCallBackService.getContractSubTaskAndParam(response.getId());
        ContractSubTask subTask = contract.getContractSubTask();
        //不是入网回调不处理
        if (!Objects.equals(subTask.getTask_type(), SUB_TASK_TASK_TYPE_CONTRACT)) {
            return;
        }
        log.info("华夏商户进件状态回调处理商户号:{}，回调参数{}", subTask.getMerchant_sn(), JSONObject.toJSONString(response));
        response.setRespMsg(response.getReason());
        ContractResponse contractResponse = handleTaskType(response, subTask, (HXParam) contract.getChannelParam());

        hxbCallBackService.hxbCallBackHandle(response.getId(), contractResponse);
    }


    /**
     * 查询处理
     *
     * @param response
     * @param contractSubTask
     * @param hxParam
     * @return
     */
    public ContractResponse handleTaskType(HXMerchantQueryResponse response, ContractSubTask contractSubTask, HXParam hxParam) {
        //商户报备
        if (SUB_TASK_TASK_TYPE_CONTRACT.equals(contractSubTask.getTask_type())) {
            return handleHXContractResponse(response, contractSubTask, hxParam);
        }
        //商户基本信息变更/更新银行卡查询 不支持

        //类型没有找到
        throw new ContractBizException("华夏渠道暂不支持");
    }

    /**
     * 入网状态处理
     *
     * @param response
     * @param contractSubTask
     * @param hxParam
     * @return
     */
    private ContractResponse handleHXContractResponse(HXMerchantQueryResponse response, ContractSubTask contractSubTask, HXParam hxParam) {
        ContractResponse contractResponse = new ContractResponse();
        contractResponse.setCode(200);
        contractResponse.setResponseParam(JSONObject.parseObject(JSONObject.toJSONString(response), Map.class));
        contractResponse.setMessage(response.getRespMsg());
        try {
            //入网成功与此状态无关 需要校验返回报文中的商户号
            if (success.contains(response.getBpmStatus())) {
                //基础校验
                baseCheck(response);
                //新增params表信息,并保存服务商终端表
                saveProviderParams(response, contractSubTask, hxParam);
            }
         }catch (ContractSysException e) {
            contractResponse.setCode(e.getCode());
            contractResponse.setMessage(e.getMessage());
        } catch (ContractException e) {
            contractResponse.setCode(Constant.RESULT_OLD_CODE);
            contractResponse.setMessage(e.getMessage());
        }
        return contractResponse;
    }


    private void baseCheck(HXMerchantQueryResponse response) {
        if (StringUtils.isBlank(response.getPayMerchantNo())) {
            throw new ContractBizException("华夏商户号不能为空");
        }
        if (StringUtils.isBlank(response.getMerchantNo())) {
            throw new ContractBizException("商户编码不能为空");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveProviderParams(HXMerchantQueryResponse response, ContractSubTask contractSubTask, HXParam hxParam) {
        Map providerParamsCon = providerTradeParamsService.getProviderParamsByRuleGroup(contractSubTask.getMerchant_sn(), contractSubTask.getRule_group_id(), 0);
        //providerParams表中有数据 说明已经处理过 直接返回
        if (WosaiMapUtils.isNotEmpty(providerParamsCon)) {
            return;
        }
        //hx payway==0
        MerchantProviderParamsDto hx = hxToProviderParams(response, contractSubTask, hxParam);

        Map merchant = merchantService.getMerchantBySn(contractSubTask.getMerchant_sn());
        if (StringUtils.isBlank(response.getWechatId()) || StringUtils.isBlank(response.getAlipayMerchantNo())) {
            HXMerchantInfoQuery infoRequest = new HXMerchantInfoQuery();
            infoRequest.setMerchantNo(response.getPayMerchantNo());
            infoRequest.setSystemNo(String.valueOf(SnowFlakeIdGenerator.getInstance().nextId()));
            infoRequest.setParentServerorgno(apolloParamsConfig.getString("hx-parentServerorgno", ""));
            infoRequest.setAppId(getHxAppIdByServerOrgNo(null));
            HXMerchantInfoQueryResponse infoQueryResponse = queryMerchantInfo(infoRequest, hxParam);
            if (infoQueryResponse.getAlipayMerchantNo() == null || !"Q".equals(infoQueryResponse.getAliMerchantStatus())){
                throw new ContractSysException("华夏支付宝支付源商户号不能为空");
            }
            if (infoQueryResponse.getWechatId() == null || !"Q".equals(infoQueryResponse.getWechatMerchantStatus())){
                throw new ContractSysException("华夏微信支付源商户号不能为空");
            }
            hx.setExtra(merchantExtra(infoQueryResponse, response, merchant));
        }else {
            //其他支付类型的交易参数 以及子商户号
            hx.setExtra(extra(response, merchant));
        }
        //状态改为已使用
        hx.setStatus(1);
        //保存provider_params表
        providerTradeParamsService.saveMerchantProviderParams(hx);
        handleProviderTerminal(contractSubTask.getMerchant_sn(), hx.getPay_merchant_id());
    }

    /**
     * 进件成功处理
     *
     * @param merchantSn
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleProviderTerminal(String merchantSn, String acquirerMerchantId) {
        //关联收单机构终端ID
        String providerTerminalId = providerTerminalIdBiz.getProviderTerminalIdForMerchant();
        //关联收单机构终端ID
        providerTerminalBiz.merchantConnectionProviderTerminal(merchantSn, providerTerminalId, acquirerMerchantId, ProviderEnum.PROVIDER_HXB.getValue());
    }


    /**
     * 商户结算信息，存储在merchant_config payway=null的params中
     * {
     * "provider_mch_id": "00001E0001",
     * "terminal_id": "60300005303",
     * "pre_terminal_id":"",
     * "longitude":"",
     * "latitude":""
     * }
     */

    public Map<String, String> tradeParamsNoPayWay(HXMerchantQueryResponse response, String merchantSn) {
        Map<String, String> value = new HashMap<>();

        value.put("provider_mch_id", response.getPayMerchantNo());
        value.put("develop_app_id", response.getMerchantAppid());


        return value;
    }


    /**
     * 组装providerParams扩展字段
     *
     * @param response
     * @param
     * @return
     */
    public Map<String, Object> merchantExtra(HXMerchantInfoQueryResponse infoQueryResponse, HXMerchantQueryResponse response, Map merchant) {
        Map<String, String> commonConfig = new HashMap<>();
        commonConfig.put(HX_PROVIDER_SERVICE_ID, apolloParamsConfig.getString("hx-parentServerorgno", ""));
        commonConfig.put(HX_PROVIDER_MCH_ID, response.getPayMerchantNo());
        Map<String, String> map = apolloParamsConfig.getMap(HX_ORGNO_APPID, "{}");
        String developAppId = Optional.ofNullable(map)
                .map(x -> x.get("parent"))
                .orElseThrow(() -> new ContractBizException("服务商appId未配置"));
        commonConfig.put(HX_DEVELOP_APP_ID, developAppId);
        //添加版本号作为公共参数
        commonConfig.put(VERSION,VERSION_NO);

        Map<String, Object> extra = Maps.newHashMap();

        //支付宝
        Map<String, Object> aliValue = new HashMap<>();
        aliValue.put(MerchantProviderParams.PAY_MERCHANT_ID, infoQueryResponse.getAlipayMerchantNo());
        aliValue.put(MerchantProviderParams.PARENT_MERCHANT_ID, response.getPayMerchantNo());
        aliValue.put(MerchantProviderParams.PROVIDER_MERCHANT_ID, response.getPayMerchantNo());
        final Map aliConfigMap = CollectionUtil.hashMap(HX_ALIPAY_SUB_MCH_ID, response.getAlipayMerchantNo());
        //一级服务商添加版本号
        aliConfigMap.put(VERSION,VERSION_NO);
        aliConfigMap.putAll(commonConfig);
        aliValue.put(TRADE, CollectionUtil.hashMap(HX_TRADE_PARAMS_KEY, aliConfigMap));

        extra.put(String.valueOf(PaywayEnum.ALIPAY.getValue()), aliValue);

        //微信
        Map<String, Object> wechatValue = new HashMap<>();
        wechatValue.put(MerchantProviderParams.PAY_MERCHANT_ID, infoQueryResponse.getWechatId());
        wechatValue.put(MerchantProviderParams.PARENT_MERCHANT_ID, response.getPayMerchantNo());
        wechatValue.put(MerchantProviderParams.PROVIDER_MERCHANT_ID, response.getPayMerchantNo());
        final Map wxConfigMap = CollectionUtil.hashMap(HX_WEIXIN_SUB_MCH_ID, response.getWechatId());
        wxConfigMap.put(VERSION,VERSION_NO);
        wxConfigMap.putAll(commonConfig);
        wechatValue.put(TRADE, CollectionUtil.hashMap(HX_TRADE_PARAMS_KEY, wxConfigMap));

        extra.put(String.valueOf(PaywayEnum.WEIXIN.getValue()), wechatValue);

        //云闪付
        Map<String, Object> unionPayValue = new HashMap<>();
        unionPayValue.put(MerchantProviderParams.PAY_MERCHANT_ID, response.getMerchantNo());
        unionPayValue.put(MerchantProviderParams.PARENT_MERCHANT_ID, response.getPayMerchantNo());
        unionPayValue.put(MerchantProviderParams.PROVIDER_MERCHANT_ID, response.getPayMerchantNo());
        // TODO: 2021/11/15 华夏暂时未返回
        final Map unionConfigMap = CollectionUtil.hashMap(HX_PROVIDER_TERM_ID, response.getTermNo());
        unionConfigMap.put(VERSION,VERSION_NO);
        unionConfigMap.putAll(commonConfig);
        unionPayValue.put(TRADE, CollectionUtil.hashMap(HX_TRADE_PARAMS_KEY, unionConfigMap));

        extra.put(String.valueOf(PaywayEnum.UNIONPAY.getValue()), unionPayValue);

        // 数币
        appendDigitalCurrencyParameters(response, commonConfig, extra);
        return extra;
    }

    /**
     * 组装providerParams扩展字段
     *
     * @param response
     * @param
     * @return
     */
    public Map<String, Object> extra(HXMerchantQueryResponse response, Map merchant) {
        Map<String, String> commonConfig = new HashMap<>();
        commonConfig.put(HX_PROVIDER_SERVICE_ID, apolloParamsConfig.getString("hx-parentServerorgno", ""));
        commonConfig.put(HX_PROVIDER_MCH_ID, response.getPayMerchantNo());
        Map<String, String> map = apolloParamsConfig.getMap(HX_ORGNO_APPID, "{}");
        String developAppId = Optional.ofNullable(map)
                .map(x -> x.get("parent"))
                .orElseThrow(() -> new ContractBizException("服务商appId未配置"));
        commonConfig.put(HX_DEVELOP_APP_ID, developAppId);
        //添加版本号作为公共参数
        commonConfig.put(VERSION, VERSION_NO);

        Map<String, Object> extra = Maps.newHashMap();

        //支付宝
        Map<String, Object> aliValue = new HashMap<>();
        aliValue.put(MerchantProviderParams.PAY_MERCHANT_ID, response.getAlipayMerchantNo());
        aliValue.put(MerchantProviderParams.PARENT_MERCHANT_ID, response.getPayMerchantNo());
        aliValue.put(MerchantProviderParams.PROVIDER_MERCHANT_ID, response.getPayMerchantNo());
        final Map aliConfigMap = CollectionUtil.hashMap(HX_ALIPAY_SUB_MCH_ID, response.getAlipayMerchantNo());
        //一级服务商添加版本号
        aliConfigMap.put(VERSION, VERSION_NO);
        aliConfigMap.putAll(commonConfig);
        aliValue.put(TRADE, CollectionUtil.hashMap(HX_TRADE_PARAMS_KEY, aliConfigMap));

        extra.put(String.valueOf(PaywayEnum.ALIPAY.getValue()), aliValue);

        //微信
        Map<String, Object> wechatValue = new HashMap<>();
        wechatValue.put(MerchantProviderParams.PAY_MERCHANT_ID, response.getWechatId());
        wechatValue.put(MerchantProviderParams.PARENT_MERCHANT_ID, response.getPayMerchantNo());
        wechatValue.put(MerchantProviderParams.PROVIDER_MERCHANT_ID, response.getPayMerchantNo());
        final Map wxConfigMap = CollectionUtil.hashMap(HX_WEIXIN_SUB_MCH_ID, response.getWechatId());
        wxConfigMap.put(VERSION, VERSION_NO);
        wxConfigMap.putAll(commonConfig);
        wechatValue.put(TRADE, CollectionUtil.hashMap(HX_TRADE_PARAMS_KEY, wxConfigMap));

        extra.put(String.valueOf(PaywayEnum.WEIXIN.getValue()), wechatValue);

        //云闪付
        Map<String, Object> unionPayValue = new HashMap<>();
        unionPayValue.put(MerchantProviderParams.PAY_MERCHANT_ID, response.getMerchantNo());
        unionPayValue.put(MerchantProviderParams.PARENT_MERCHANT_ID, response.getPayMerchantNo());
        unionPayValue.put(MerchantProviderParams.PROVIDER_MERCHANT_ID, response.getPayMerchantNo());
        // TODO: 2021/11/15 华夏暂时未返回
        final Map unionConfigMap = CollectionUtil.hashMap(HX_PROVIDER_TERM_ID, response.getTermNo());
        unionConfigMap.put(VERSION, VERSION_NO);
        unionConfigMap.putAll(commonConfig);
        unionPayValue.put(TRADE, CollectionUtil.hashMap(HX_TRADE_PARAMS_KEY, unionConfigMap));

        extra.put(String.valueOf(PaywayEnum.UNIONPAY.getValue()), unionPayValue);

        // 数币
        appendDigitalCurrencyParameters(response, commonConfig, extra);
        return extra;
    }

    private void appendDigitalCurrencyParameters(HXMerchantQueryResponse response, Map<String, String> commonConfig, Map<String, Object> extra) {
        Map<String, Object> hxDigitalCurrencyParamMap = Maps.newHashMap();
        hxDigitalCurrencyParamMap.put(MerchantProviderParams.PAY_MERCHANT_ID, response.getDcMerchantNo());
        hxDigitalCurrencyParamMap.put(MerchantProviderParams.PARENT_MERCHANT_ID, response.getPayMerchantNo());
        hxDigitalCurrencyParamMap.put(MerchantProviderParams.PROVIDER_MERCHANT_ID, response.getPayMerchantNo());
        final Map<String, Object> dcConfigMap = CollectionUtil.hashMap(HX_PROVIDER_TERM_ID, response.getTermNo());
        dcConfigMap.putAll(commonConfig);
        hxDigitalCurrencyParamMap.put(TRADE, CollectionUtil.hashMap(HX_TRADE_PARAMS_KEY, dcConfigMap));
        extra.put(String.valueOf(PaywayEnum.DCEP.getValue()), hxDigitalCurrencyParamMap);
    }


    /**
     * 构建阿里终端绑定信息
     *
     * @param dto         绑定参数
     * @param hxParam     华夏基础信息
     * @param operationId 本次操作标识  I:新增 U:修改 D:注销
     * @param payWay      支付方式  2,3,17
     * @return
     */
    public ContractResponse termInfoOperate(BaseTermInfoDTO dto, HXParam hxParam, String operationId, Integer payWay) {
        ContractResponse response = new ContractResponse();
        try {
            final String merchantSn = dto.getMerchantSn();
            final Map merchant = merchantService.getMerchantByMerchantSn(merchantSn);
            final HXTerminal hxTerminal = new HXTerminal();
            //根据8位终端号查询该终端对应的收钱吧终端信息
            Map terminal = getTerminal(merchantSn, dto.getDeviceId());
            if (MapUtils.isNotEmpty(terminal) && Objects.equals(QM50, BeanUtil.getPropString(terminal, Terminal.VENDOR_APP_APPID))) {
                hxTerminal.setTermType("20");
                hxTerminal.setPosSerialNo(BeanUtil.getPropString(terminal, Terminal.DEVICE_FINGERPRINT));
                hxTerminal.setTermProducerName("联迪");
                hxTerminal.setTermProductModel("QM50");
            }
            //组装参数
            //一级服务商
            hxTerminal.setParentServerorgno(apolloParamsConfig.getString("hx-parentServerorgno", ""));
            //二级服务商非必填,这个配置校验防止有问题
            boolean open = apolloParamsConfig.getBoolean("hx_terminal_serverOrgNo", Boolean.FALSE);
            if (open) {
                String province = BeanUtil.getPropString(merchant, Merchant.PROVINCE);
                String city = BeanUtil.getPropString(merchant, Merchant.CITY);
                String district = BeanUtil.getPropString(merchant, Merchant.DISTRICT);
                String orgNo = getOrgNo(province, city, district);
                if (StringUtils.isBlank(orgNo)) {
                    throw new ContractBizException("商户所在地区暂不支持入网华夏银行");
                }
                hxTerminal.setServerOrgNo(orgNo);
            }
            String acquireMerchantId;
            if (dto instanceof AddTermInfoDTO && StringUtils.isNotBlank(((AddTermInfoDTO) dto).getProviderMerNo())) {
                acquireMerchantId = ((AddTermInfoDTO) dto).getProviderMerNo();
            } else {
                acquireMerchantId = getAcquireMerchantId(merchantSn, hxParam.getProvider());
            }
            hxTerminal.setPayMerchantNo(acquireMerchantId);
            //生产环境终端号保持现有规则,测试环境终端号以LTCS开头
            hxTerminal.setTermNo("prod,vpc".contains(env) ? dto.getDeviceId() : "LTCS" + dto.getDeviceId().substring(4));
            String storeSn = dto.getStoreSn();
            Map<String, String> hxTerminalAddress = contractWith259Biz.getHxTerminalAddress(storeSn, merchantSn);
            final String streetAddress = MapUtils.getString(hxTerminalAddress, Merchant.STREET_ADDRESS);
            hxTerminal.setDeployAddress(StringFilter.filter(streetAddress));
            hxTerminal.setDeployAddressProvince(MapUtils.getString(hxTerminalAddress, PROVINCE_CODE));
            hxTerminal.setDeployAddressCity(MapUtils.getString(hxTerminalAddress, CITY_CODE));
            hxTerminal.setDeployAddressArea(MapUtils.getString(hxTerminalAddress, DISTRICT_CODE));
            if (Objects.equals(payWay, MerchantProviderParams.PAYWAY_ALIPAY2)) {
                hxTerminal.setAliRegistStatus(operationId);
            }
            if (Objects.equals(payWay, MerchantProviderParams.PAYWAY_WEIXIN)) {
                hxTerminal.setWechatRegistStatus(operationId);
            }
            if (Objects.equals(payWay, MerchantProviderParams.PAYWAY_LKL_UNIONPAY)) {
                hxTerminal.setUnionRegistStatus(operationId);
            }
            //发送请求
            final HXBaseResponse hxBaseResponse = termRegist(hxTerminal, hxParam);
            //新增终端操作时报错"已存在"或者"进行状态变更"
            if (Objects.equals(operationId, "I")
                    && (StrUtil.contains(hxBaseResponse.getRespMsg(), "进行状态变更") || StrUtil.contains(hxBaseResponse.getRespMsg(), "该终端号已存在"))) {
                return termInfoOperate(dto, hxParam, "U", payWay);
            }
            response.setRequestParam(objectMapper.convertValue(hxTerminal, Map.class))
                    .setResponseParam(JSONObject.parseObject(JSONObject.toJSONString(hxBaseResponse), Map.class));
            response = handleResponse(hxBaseResponse, "华夏终端绑定/解绑成功", merchantSn, response);
        } catch (ContractException e) {
            response.setCode(Objects.isNull(e.getCode()) ? RESULT_OLD_CODE : e.getCode());
            response.setMessage(e.getMessage());
        } catch (Exception e) {
            log.error("华夏系统异常{}", e);
            response.setCode(RESULT_CODE_SYSTEM_EXCEPTION);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    /**
     * 终端注册
     *
     * @param request
     * @param hxParam
     * @return
     */
    public HXBaseResponse termRegist(HXTerminal request, HXParam hxParam) {
        //参数组
        String content = JSONObject.toJSONString(request);
        log.info("华夏终端注册请求参数:{}", content);
        final String serverOrgNo = request.getServerOrgNo();
        final String hxAppId = getHxAppIdByServerOrgNo(serverOrgNo);
        //调用请求接口
        String send = HXHttpUtils.send(content, hxParam, TERM_REGIST, requestUrl, hxAppId);
        return JSON.parseObject(send, HXMerchantQueryResponse.class);
    }


    /**
     * 终端注册结果查询
     *
     * @param request
     * @param hxParam
     * @return
     */
    public HXTerminalQueryResponse getTermRegistResult(HXTerminalQuery request, HXParam hxParam, String hxAppId) {
        //参数组
        String content = JSONObject.toJSONString(request);
        log.info("华夏终端注册结果查询请求参数:{}", content);
        //调用请求接口
        String send = HXHttpUtils.send(content, hxParam, GET_TERM_REGIST_RESULT, requestUrl, hxAppId);
        return JSON.parseObject(send, HXTerminalQueryResponse.class);
    }


    /**
     * 华夏返回处理
     *
     * @param hxBaseResponse
     * @param message
     * @param merchantSn
     * @return
     */
    public ContractResponse handleResponse(HXBaseResponse hxBaseResponse, String message, String merchantSn, ContractResponse response) {
        try {
            //返回参数判断
            if (HXConstant.SUCCESS.equals(hxBaseResponse.getRespCode())) {
                response.setCode(RESULT_CODE_SUCCESSS);
                response.setMessage(message);
            } else {
                //业务异常
                if (apolloParamsConfig.getString(ApolloConfigParams.HX_ERROR_CODE, BIZ_ERROR).contains(hxBaseResponse.getRespCode())
                        && !apolloParamsConfig.getList(ApolloConfigParams.HX_SYSTEM_ERROR_MESSAGE, "[\"终端信息采集失败\",\"地图服务异常\"]")
                        .contains(hxBaseResponse.getRespMsg())) {
                    throw new ContractBizException(hxBaseResponse.getRespMsg());
                } else {
                    //系统异常
                    throw new ContractSysException(hxBaseResponse.getRespMsg());
                }
            }
        } catch (ContractException e) {
            log.error("华夏业务异常:商户号：{},{}", merchantSn, e);
            response.setCode(Objects.isNull(e.getCode()) ? RESULT_OLD_CODE : e.getCode());
            response.setMessage(e.getMessage());
        } catch (Exception e) {
            log.error("华夏系统异常:商户号：{} ,{}", merchantSn, e);
            response.setCode(RESULT_CODE_SYSTEM_EXCEPTION);
            response.setMessage(e.getMessage());
        }
        return response;
    }


    /**
     * 商户在收单机构的商户号
     *
     * @param merchantSn 商户号
     * @return
     */
    private String getAcquireMerchantId(String merchantSn, String provider) {
        //商户在收单机构的商户号
        Criteria criteria = Criteria.where(MerchantProviderParams.MERCHANT_SN).is(merchantSn).
                with(MerchantProviderParams.PROVIDER).is(provider)
                .with(MerchantProviderParams.PAYWAY).is(0).with(MerchantProviderParams.DELETED).is(0);
        final Map<String, Object> merchantProviderParams = merchantProviderParamsDao.filter(criteria).fetchOne();
        //商户号
        return BeanUtil.getPropString(merchantProviderParams, MerchantProviderParams.PAY_MERCHANT_ID);
    }


    /**
     * @param subMchId 根据子商户号获取华夏商户号
     * @return String
     * <AUTHOR>
     * @Description:
     * @time 15:02
     */
    public String getHxMerNo(String subMchId) {
        //根据子商户号获取华夏商户号
        Criteria criteria = Criteria.where(MerchantProviderParams.PAY_MERCHANT_ID).is(subMchId);
        final Map<String, Object> merchantProviderParams = merchantProviderParamsDao.filter(criteria).fetchOne();
        //华夏商户号
        return Optional.ofNullable(merchantProviderParams).map(param -> BeanUtil.getPropString(merchantProviderParams, MerchantProviderParams.PROVIDER_MERCHANT_ID)).orElseThrow(() -> new ContractBizException("没有找到华夏商户号"));
    }

    /**
     * @param hxMerNo 华夏商户号
     * @return String
     * <AUTHOR>
     * @Description: 根据华夏商户号获取appId
     * @time 15:02
     */
    public String getHxAppIdByHxMerNo(String hxMerNo) {
        //根据serverOrgNo获取appId
        final String hxAppId = getHxAppIdByServerOrgNo(null);
        return hxAppId;
    }

    /**
     * 根据服务商编码获取华夏APPID
     *
     * @param orgNo
     * @return
     */
    public String getHxAppIdByServerOrgNo(String orgNo) {
        Map<String, String> map = apolloParamsConfig.getMap(HX_ORGNO_APPID, "{}");
        return Optional.ofNullable(map).map(x -> x.get("parent")).orElseThrow(() -> new ContractBizException("服务商appId未配置"));
    }


    /**
     * 根据8位终端号查询该终端对应的收钱吧终端信息
     * @param merchantSn 商户号
     * @param providerTerminalId 收单机构终端号
     * @return
     */
    public Map getTerminal(String merchantSn, String providerTerminalId) {
        Map<String, Object> providerTerminal = getProviderTerminal(merchantSn, providerTerminalId);
        String terminalSn = BeanUtil.getPropString(providerTerminal, "terminal_sn");
        if (StringUtils.isBlank(terminalSn)) {
            return null;
        }
        Map terminal = terminalService.getTerminalBySn(terminalSn);
        return terminal;
    }

    /**
     * 获取终端绑定信息
     * @param merchantSn
     * @param providerTerminalId
     * @return
     */
    @Nullable
    public Map<String, Object> getProviderTerminal(String merchantSn, String providerTerminalId) {
        //商户在收单机构的商户号
        Criteria criteria = Criteria.where("merchant_sn").is(merchantSn).
                with("provider").is(ProviderEnum.PROVIDER_HXB.getValue()).
                with("provider_terminal_id").is(providerTerminalId);
        Map<String, Object> providerTerminal = null;
        final Iterator<Map<String, Object>> mapIterator = providerTerminalDao.filter(criteria).fetchAll();
        while (Objects.nonNull(mapIterator) && mapIterator.hasNext()) {
            Map<String, Object> map = mapIterator.next();
            if (Objects.equals(providerTerminalId, MapUtils.getString(map, "provider_terminal_id"))) {
                providerTerminal = map;
                break;
            }
        }
        return providerTerminal;
    }

    @Nullable
    public Map<String, Object> getProviderTerminal(String providerTerminalId) {
        //商户在收单机构的商户号
        Criteria criteria = Criteria.where("provider").is(ProviderEnum.PROVIDER_HXB.getValue()).
                with("provider_terminal_id").is(providerTerminalId);
        Map<String, Object> providerTerminal = null;
        final Iterator<Map<String, Object>> mapIterator = providerTerminalDao.filter(criteria).fetchAll();
        while (Objects.nonNull(mapIterator) && mapIterator.hasNext()) {
            Map<String, Object> map = mapIterator.next();
            if (Objects.equals(providerTerminalId, MapUtils.getString(map, "provider_terminal_id"))) {
                providerTerminal = map;
                break;
            }
        }
        return providerTerminal;
    }


    /**
     * 构建阿里终端绑定信息
     *
     * @param termId         8位终端号
     * @param operationId 本次操作标识  I:新增 U:修改 D:注销
     * @param payWay      支付方式  2,3,17
     * @return
     */
    public ContractResponse termInfoOperate(String termId, String operationId, Integer payWay) {
        ContractResponse response = new ContractResponse();
        try {
            final Map<String, Object> providerTerminal = getProviderTerminal(termId);
            final String merchantSn = MapUtils.getString(providerTerminal, "merchant_sn");
            final HXTerminal hxTerminal = new HXTerminal();
            //根据8位终端号查询该终端对应的收钱吧终端信息
            Map terminal = getTerminal(merchantSn, termId);
            if (MapUtils.isNotEmpty(terminal) && Objects.equals(QM50, BeanUtil.getPropString(terminal, Terminal.VENDOR_APP_APPID))) {
                hxTerminal.setTermType("20");
                hxTerminal.setPosSerialNo(BeanUtil.getPropString(terminal, Terminal.DEVICE_FINGERPRINT));
                hxTerminal.setTermProducerName("联迪");
                hxTerminal.setTermProductModel("QM50");
            }
            //组装参数
            //一级服务商
            hxTerminal.setParentServerorgno(apolloParamsConfig.getString("hx-parentServerorgno", ""));
            String acquireMerchantId = getAcquireMerchantId(merchantSn, String.valueOf(ProviderEnum.PROVIDER_HXB.getValue()));
            hxTerminal.setPayMerchantNo(acquireMerchantId);
            //生产环境终端号保持现有规则,测试环境终端号以LTCS开头
            hxTerminal.setTermNo("prod,vpc".contains(env) ? termId : "LTCS" + termId.substring(4));
            //如果storeSn则是商户级别的终端
            String storeSn = MapUtils.getString(providerTerminal, "store_sn");
            Map<String, String> hxTerminalAddress = contractWith259Biz.getHxTerminalAddress(storeSn, merchantSn);
            final String streetAddress = MapUtils.getString(hxTerminalAddress, Merchant.STREET_ADDRESS);
            hxTerminal.setDeployAddress(StringFilter.filter(streetAddress));
            hxTerminal.setDeployAddressProvince(MapUtils.getString(hxTerminalAddress, PROVINCE_CODE));
            hxTerminal.setDeployAddressCity(MapUtils.getString(hxTerminalAddress, CITY_CODE));
            hxTerminal.setDeployAddressArea(MapUtils.getString(hxTerminalAddress, DISTRICT_CODE));
            if (Objects.equals(payWay, MerchantProviderParams.PAYWAY_ALIPAY2)) {
                hxTerminal.setAliRegistStatus(operationId);
            }
            if (Objects.equals(payWay, MerchantProviderParams.PAYWAY_WEIXIN)) {
                hxTerminal.setWechatRegistStatus(operationId);
            }
            if (Objects.equals(payWay, MerchantProviderParams.PAYWAY_LKL_UNIONPAY)) {
                hxTerminal.setUnionRegistStatus(operationId);
            }
            //发送请求
            final HXBaseResponse hxBaseResponse = termRegist(hxTerminal, getHXParam());
            if (Objects.equals(operationId, "I")
                    && (StrUtil.contains(hxBaseResponse.getRespMsg(), "进行状态变更") || StrUtil.contains(hxBaseResponse.getRespMsg(), "该终端号已存在"))) {
                return termInfoOperate(termId, "U", payWay);
            }
            response.setRequestParam(objectMapper.convertValue(hxTerminal, Map.class))
                    .setResponseParam(JSONObject.parseObject(JSONObject.toJSONString(hxBaseResponse), Map.class));
            response = handleResponse(hxBaseResponse, "华夏终端绑定/解绑成功", merchantSn, response);
        } catch (ContractException e) {
            response.setCode(Objects.isNull(e.getCode()) ? RESULT_OLD_CODE : e.getCode());
            response.setMessage(e.getMessage());
        } catch (Exception e) {
            log.error("华夏系统异常", e);
            response.setCode(RESULT_CODE_SYSTEM_EXCEPTION);
            response.setMessage(e.getMessage());
        }
        return response;
    }


    /**
     * 获取富友参数
     * @return
     */
    public HXParam getHXParam() {
        MapSqlParameterSource parameters = new MapSqlParameterSource()
                .addValue(MerchantProviderParams.PROVIDER, ProviderEnum.PROVIDER_HXB.getValue())
                .addValue(MerchantProviderParams.PAYWAY, PaywayEnum.ACQUIRER.getValue());
        Map mcChannel = namedParameterJdbcTemplate.queryForMap("select * from mc_channel where payway = :payway and provider = :provider", parameters);
        return JSONObject.parseObject(MapUtils.getString(mcChannel, "acquirer_metadata"), HXParam.class);
    }

}
