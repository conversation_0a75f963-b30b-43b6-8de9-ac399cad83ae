package com.wosai.upay.merchant.contract.model.fuyou;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 富友费率配置
 *
 * <AUTHOR>
 * @date 2024/9/20
 */
@Data
@Accessors(chain = true)
public class MchntSetCd {

    /**
     * 交易类型描述
     */
    private String busiCd;

    /**
     * 卡种（00：借记卡，02：贷记卡）
     */
    private String cardType;

    /**
     * 交易计费（扣率）
     */
    private String setCd;

    /**
     * 活动扣率（1000元以下的银联二维码扣率）
     */
    private String spcSetCd;

}
