package com.wosai.upay.merchant.contract.utils;

import com.wosai.upay.job.service.ContractApplicationService;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/10/8
 */
public class CustomFieldsUtil {

    public static void fillCustomFields(Map requestParams, Map contextParam) {
        final Map customFields = (Map) contextParam.get(ContractApplicationService.KEY_CUSTOM_FIELDS);
        if (Objects.isNull(customFields)) {
            return;
        }
        mergeNestedMaps(requestParams, customFields);
    }

    public static void mergeNestedMaps(Map<String, Object> target, Map<String, Object> source) {
        for (Map.Entry<String, Object> entry : source.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            // 如果值是Map类型，则递归调用mergeNestedMaps
            if (value instanceof Map && target.containsKey(key) && target.get(key) instanceof Map) {
                Map<String, Object> targetSubMap = (Map<String, Object>) target.get(key);
                Map<String, Object> sourceSubMap = (Map<String, Object>) value;
                mergeNestedMaps(targetSubMap, sourceSubMap);
            } else {
                // 否则直接覆盖或者添加到目标Map
                target.put(key, value);
            }
        }
    }

}
