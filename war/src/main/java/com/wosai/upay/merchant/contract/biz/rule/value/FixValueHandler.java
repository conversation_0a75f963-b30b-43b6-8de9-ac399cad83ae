package com.wosai.upay.merchant.contract.biz.rule.value;

import com.wosai.upay.merchant.contract.biz.rule.SupportUtil;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 取固定值
 *
 * <AUTHOR>
 * @date 2020-09-03
 */
@Component
public class FixValueHandler extends ValueRuleHandler {
    @Override
    public int getOrder() {
        return 98;
    }

    @Override
    public String supportRule() {
        return "fix_value";
    }

    @Override
    protected Object doApply(Map<String, Object> source, Object ruleDetail) {
        return ruleDetail;
    }


}
