package com.wosai.upay.merchant.contract.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 富友基本信息变更人工审核请求DTO
 *
 * <AUTHOR>
 * @date 2025/4/21 14:49
 */
@Data
public class FuyouBasicInfoUpdateManalReviewReqDTO {

    /**
     * 唯一流水号12
     */
    @JsonProperty("trace_no")
    @JSONField(name = "trace_no")
    private String traceNo;
    /**
     * 已在富友入网的富友商户代码
     */
    @JsonProperty("fy_mchnt_cd")
    @JSONField(name = "fy_mchnt_cd")
    private String fyMchntCd;
    /**
     * 机构号,接入机构在富友的唯一代码
     */
    @JsonProperty("ins_cd")
    @JSONField(name = "ins_cd")
    private String insCd;
    /**
     * 二级代理机构号
     */
    @JsonProperty("sub_ins_cd")
    @JSONField(name = "sub_ins_cd")
    private String subInsCd;
    /**
     * 不能有特殊字符，仅可包含汉字、数字、字母
     */
    @JsonProperty("mchnt_name")
    @JSONField(name = "mchnt_name")
    private String mchntName;
    /**
     * 商户英文名称
     */
    @JsonProperty("mchnt_en_name")
    @JSONField(name = "mchnt_en_name")
    private String mchntEnName;
    /**
     * 商户简称不能超过，不能有特殊字符，仅可包含汉字、数字、字母
     */
    @JsonProperty("mchnt_shortname")
    @JSONField(name = "mchnt_shortname")
    private String mchntShortname;
    /**
     * 商户英文简称
     */
    @JsonProperty("mchnt_en_shortname")
    @JSONField(name = "mchnt_en_shortname")
    private String mchntEnShortname;
    /**
     * 商户真实名称（与营业执照上相同）
     */
    @JsonProperty("real_name")
    @JSONField(name = "real_name")
    private String realName;
    /**
     * 品牌名称
     */
    @JsonProperty("plat_name_cn")
    @JSONField(name = "plat_name_cn")
    private String platNameCn;
    /**
     * 经营范围
     */
    @JsonProperty("trade_desc")
    @JSONField(name = "trade_desc")
    private String tradeDesc;
    /**
     * 证件类型：0营业执照，1三证合一，A身份证（一证下机）B 个体户，2事业单位
     */
    @JsonProperty("license_type")
    @JSONField(name = "license_type")
    private String licenseType;
    /**
     * 证件号码，填写方法：1.license_type=0或1，此处填写营业执照号码。2.license_type=A，此处填写身份证号码3.license_type=B，此处填写个体工商户营业执照号码
     */
    @JsonProperty("license_no")
    @JSONField(name = "license_no")
    private String licenseNo;
    /**
     * 证件到期日（格式YYYYMMDD）格式长期请填20991231 无有效期请填190001011.license_type=0或1，此处填写营业执照到期日。2.license_type=A此处填写身份证的到期日3.license_type=B，此处填写个体工商户营业执照号的到期日
     */
    @JsonProperty("license_expire_dt")
    @JSONField(name = "license_expire_dt")
    private String licenseExpireDt;
    /**
     * 营业执照开始时间
     */
    @JsonProperty("license_start_dt")
    @JSONField(name = "license_start_dt")
    private String licenseStartDt;
    /**
     * 注册地址
     */
    @JsonProperty("lic_regis_addr")
    @JSONField(name = "lic_regis_addr")
    private String licRegisAddr;
    /**
     * 注册资金
     */
    @JsonProperty("reg_capital")
    @JSONField(name = "reg_capital")
    private String regCapital;
    /**
     * 组织机构代码证
     */
    @JsonProperty("zzjgdmz_no")
    @JSONField(name = "zzjgdmz_no")
    private String zzjgdmzNo;
    /**
     * 组织机构代码证到期时间
     */
    @JsonProperty("zzjgdmz_expire_dt")
    @JSONField(name = "zzjgdmz_expire_dt")
    private String zzjgdmzExpireDt;
    /**
     * 税务登记证
     */
    @JsonProperty("tax_no")
    @JSONField(name = "tax_no")
    private String taxNo;
    /**
     * 税务登记证到期时间
     */
    @JsonProperty("tax_dt")
    @JSONField(name = "tax_dt")
    private String taxDt;
    /**
     * 法人身份证号
     */
    @JsonProperty("certif_id")
    @JSONField(name = "certif_id")
    private String certifId;
    /**
     * 法人身份证到期日（格式YYYYMMDD）
     */
    @JsonProperty("certif_id_expire_dt")
    @JSONField(name = "certif_id_expire_dt")
    private String certifIdExpireDt;
    /**
     * 法人身份证开始时间
     */
    @JsonProperty("card_start_dt")
    @JSONField(name = "card_start_dt")
    private String cardStartDt;
    /**
     * 法人姓名
     */
    @JsonProperty("artif_nm")
    @JSONField(name = "artif_nm")
    private String artifNm;
    /**
     * 法人证件类型
     */
    @JsonProperty("artif_tp")
    @JSONField(name = "artif_tp")
    private String artifTp;
    /**
     * 法人手机号
     */
    @JsonProperty("certif_phone")
    @JSONField(name = "certif_phone")
    private String certifPhone;
    /**
     * 联系人姓名
     */
    @JsonProperty("contact_person")
    @JSONField(name = "contact_person")
    private String contactPerson;
    /**
     * 客服电话
     */
    @JsonProperty("contact_phone")
    @JSONField(name = "contact_phone")
    private String contactPhone;
    /**
     * 联系电话（必须真实手机号）
     */
    @JsonProperty("contact_mobile")
    @JSONField(name = "contact_mobile")
    private String contactMobile;
    /**
     * 联系邮箱
     */
    @JsonProperty("contact_email")
    @JSONField(name = "contact_email")
    private String contactEmail;
    /**
     * 联系地址
     */
    @JsonProperty("contact_addr")
    @JSONField(name = "contact_addr")
    private String contactAddr;
    /**
     * 联系人身份证号
     */
    @JsonProperty("contact_cert_no")
    @JSONField(name = "contact_cert_no")
    private String contactCertNo;
    /**
     * 联系人身份证到期日
     */
    @JsonProperty("contact_expire_dt")
    @JSONField(name = "contact_expire_dt")
    private String contactExpireDt;
    /**
     * 变更描述即变更备注，富友在审核时，可以看到的文字描述
     */
    @JsonProperty("modify_desc")
    @JSONField(name = "modify_desc")
    private String modifyDesc;
    /**
     * 签名
     */
    @JsonProperty("sign")
    @JSONField(name = "sign")
    private String sign;

}
