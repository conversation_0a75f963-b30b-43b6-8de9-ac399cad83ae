package com.wosai.upay.merchant.contract.constant.ccb;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/3
 */
public class CcbRequestConstant {

    //---------------------请求返回值参数-------------------------

    public static final String SUCCESS_REQ = "Y";

    public static final String WAIT_FOR_SIGN = "YBLA06412007";

    public static final String SYS_FAIL_CODES = "";

    //---------------------终端列表相关参数------------------------

    /**
     * pos终端编号
     */
    public static final String POS_ID = "POS_ID";
    /**
     * 柜台名称
     * 上送商户名
     */
    public static final String WBG_CNTER_NM = "Wbg_Cnter_Nm";
    /**
     * 结算账号标志
     * 01-本行对公
     * 02-本行对私
     */
    public static final String SETL_ACCNO_IND = "Setl_AccNo_Ind";
    /**
     * 结算支付账号
     * number
     */
    public static final String SETL_PY_ACC_NO = "Setl_Py_AccNo";
    /**
     * 结算支付账号开户行号
     * 不上送
     */
    public static final String SETL_PY_ACC_NO_DEP_BNK_NO = "SetlPyAccNo_DepBnk_No";
    /**
     * 结算支付账户名称
     * holder
     */
    public static final String SETL_PY_ACC_NM = "Setl_Py_AccNm";
    /**
     * DCEP钱包账号
     * 暂不上送
     */
    public static final String CST_ACC_NO = "Cst_AccNo";
    /**
     * DCEP钱包账号名称
     * 暂不上送
     */
    public static final String ID_CST_ACC_NO_NM = "IdCst_AccNo_Nm";
    /**
     * DCEP钱包账号类型
     * 暂不上送
     */
    public static final String AR_ACC_TP_CD = "Ar_Acc_TpCd";
    /**
     * DCEP钱包账号开户机构号
     * 暂不上送
     */
    public static final String DP_BK_INNO = "DpBkInNo";

    public static final List<String> TERMINAL_LIST = Arrays.asList(WBG_CNTER_NM, SETL_ACCNO_IND, SETL_PY_ACC_NO, SETL_PY_ACC_NO_DEP_BNK_NO
            , SETL_PY_ACC_NM, CST_ACC_NO, ID_CST_ACC_NO_NM, AR_ACC_TP_CD, DP_BK_INNO);


    //---------------------费率列表相关参数------------------------
    /**
     * 费率类型
     * 费率类型取值范围：
     * TH 他行借记卡扣率
     * TD 他行贷记卡扣率
     * DJ 本行贷记卡
     * FD 本行非贷记
     * WH  微信借记卡
     * WD  微信贷记卡
     * ZH  支付宝借记卡
     * ZD  支付宝贷记卡
     */
    public static final String ACQ_CMSN_CHRG_RATE_TP_CD = "Acq_CmsnChrgRate_TpCd";
    /**
     * 商户手续费上送
     */
    public static final String FST_LVL_HD_CG_RATE = "Fst_Lvl_HdCg_Rate";
    /**
     * 手续费相关
     */
    public static final String FEE_DIS_LIST = "FEE_DIS_LIST";
    /**
     * 手续费分润一方代码
     * 默认送2-分润到对公账号
     */
    public static final String HD_CG_PR_DSB_PTY_1_CD = "HdCg_PrDsbPty_1_Cd";
    /**
     * 分润对公账号
     */
    public static final String PR_DSB_PTY1_APNT_ACC_NO = "PrDsbPty1_Apnt_Acc_No";
    /**
     * 回佣分润方分配比例值
     */
    public static final String DCN_PR_DSB_PY_ALCT_PCTG_VAL = "DcnPrDsbPyAlctPctgVal";


    public static final List<String> FEE_RATES = Arrays.asList(ACQ_CMSN_CHRG_RATE_TP_CD, FST_LVL_HD_CG_RATE, FEE_DIS_LIST);

    public static final List<String> FEE_RATES_UPDATE = Arrays.asList(POS_ID, ACQ_CMSN_CHRG_RATE_TP_CD, FST_LVL_HD_CG_RATE, FEE_DIS_LIST);

    //--------------以下是文件列表---------
    /**
     * 文件类型编码
     * 01-营业执照
     * 02-组织机构代码
     * 05-税务登记证或完税证明
     * 06-法定代表人或负责人证件
     * 09-租赁合同
     * 10-门店照片
     * 11-民办非企业单位证书
     * 12-水电缴费证明
     * 13-特许经营行业许可证
     * 14-ICP证
     * 18-结算账户证明文件
     * 19-财务状况证明文件
     * 20-个人征信查询授权书
     * 21-企业征信查询授权书
     * 22-合法资金管理关系证明
     * 99-其他
     */
    public static final String FILE_ECD = "FILE_ECD";

    public static final String FILE_TP_ECD = "FILE_TP_ECD";
    /**
     * 文件名称
     */
    public static final String ATCH_FILE_NM = "Atch_File_Nm";
    /**
     * 文件编号
     */
    public static final String BSN_ID = "Bsn_ID";
    /**
     * 文件大小
     */
    public static final String ELTC_FILES_FILE_SZ = "Eltc_Files_File_Sz";

    public static final List<String> FILES = Arrays.asList(FILE_ECD, ATCH_FILE_NM, BSN_ID, ELTC_FILES_FILE_SZ);

    public static final List<String> DECP_FILES = Arrays.asList(FILE_TP_ECD, ATCH_FILE_NM, BSN_ID, ELTC_FILES_FILE_SZ);
}
