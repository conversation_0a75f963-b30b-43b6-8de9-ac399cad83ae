/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.wosai.upay.merchant.contract.avro;

import org.apache.avro.specific.SpecificData;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.SchemaStore;

@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class MerchantBankPreAvro extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = 6933254219170070005L;
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"MerchantBankPreAvro\",\"namespace\":\"com.wosai.upay.merchant.contract.avro\",\"fields\":[{\"name\":\"id\",\"type\":[\"string\",\"null\"]},{\"name\":\"merchant_id\",\"type\":[\"string\",\"null\"]},{\"name\":\"request_params\",\"type\":[\"null\",\"string\"]},{\"name\":\"verify_status\",\"type\":\"int\"},{\"name\":\"contract_memo\",\"type\":[\"string\",\"null\"]},{\"name\":\"merchant_sn\",\"type\":[\"string\",\"null\"]},{\"name\":\"contract_id\",\"type\":[\"string\",\"null\"]},{\"name\":\"mtime\",\"type\":\"long\"}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<MerchantBankPreAvro> ENCODER =
      new BinaryMessageEncoder<MerchantBankPreAvro>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<MerchantBankPreAvro> DECODER =
      new BinaryMessageDecoder<MerchantBankPreAvro>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   */
  public static BinaryMessageDecoder<MerchantBankPreAvro> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   */
  public static BinaryMessageDecoder<MerchantBankPreAvro> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<MerchantBankPreAvro>(MODEL$, SCHEMA$, resolver);
  }

  /** Serializes this MerchantBankPreAvro to a ByteBuffer. */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /** Deserializes a MerchantBankPreAvro from a ByteBuffer. */
  public static MerchantBankPreAvro fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  @Deprecated public java.lang.CharSequence id;
  @Deprecated public java.lang.CharSequence merchant_id;
  @Deprecated public java.lang.CharSequence request_params;
  @Deprecated public int verify_status;
  @Deprecated public java.lang.CharSequence contract_memo;
  @Deprecated public java.lang.CharSequence merchant_sn;
  @Deprecated public java.lang.CharSequence contract_id;
  @Deprecated public long mtime;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public MerchantBankPreAvro() {}

  /**
   * All-args constructor.
   * @param id The new value for id
   * @param merchant_id The new value for merchant_id
   * @param request_params The new value for request_params
   * @param verify_status The new value for verify_status
   * @param contract_memo The new value for contract_memo
   * @param merchant_sn The new value for merchant_sn
   * @param contract_id The new value for contract_id
   * @param mtime The new value for mtime
   */
  public MerchantBankPreAvro(java.lang.CharSequence id, java.lang.CharSequence merchant_id, java.lang.CharSequence request_params, java.lang.Integer verify_status, java.lang.CharSequence contract_memo, java.lang.CharSequence merchant_sn, java.lang.CharSequence contract_id, java.lang.Long mtime) {
    this.id = id;
    this.merchant_id = merchant_id;
    this.request_params = request_params;
    this.verify_status = verify_status;
    this.contract_memo = contract_memo;
    this.merchant_sn = merchant_sn;
    this.contract_id = contract_id;
    this.mtime = mtime;
  }

  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
  // Used by DatumWriter.  Applications should not call.
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return id;
    case 1: return merchant_id;
    case 2: return request_params;
    case 3: return verify_status;
    case 4: return contract_memo;
    case 5: return merchant_sn;
    case 6: return contract_id;
    case 7: return mtime;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  // Used by DatumReader.  Applications should not call.
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: id = (java.lang.CharSequence)value$; break;
    case 1: merchant_id = (java.lang.CharSequence)value$; break;
    case 2: request_params = (java.lang.CharSequence)value$; break;
    case 3: verify_status = (java.lang.Integer)value$; break;
    case 4: contract_memo = (java.lang.CharSequence)value$; break;
    case 5: merchant_sn = (java.lang.CharSequence)value$; break;
    case 6: contract_id = (java.lang.CharSequence)value$; break;
    case 7: mtime = (java.lang.Long)value$; break;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  /**
   * Gets the value of the 'id' field.
   * @return The value of the 'id' field.
   */
  public java.lang.CharSequence getId() {
    return id;
  }

  /**
   * Sets the value of the 'id' field.
   * @param value the value to set.
   */
  public void setId(java.lang.CharSequence value) {
    this.id = value;
  }

  /**
   * Gets the value of the 'merchant_id' field.
   * @return The value of the 'merchant_id' field.
   */
  public java.lang.CharSequence getMerchantId() {
    return merchant_id;
  }

  /**
   * Sets the value of the 'merchant_id' field.
   * @param value the value to set.
   */
  public void setMerchantId(java.lang.CharSequence value) {
    this.merchant_id = value;
  }

  /**
   * Gets the value of the 'request_params' field.
   * @return The value of the 'request_params' field.
   */
  public java.lang.CharSequence getRequestParams() {
    return request_params;
  }

  /**
   * Sets the value of the 'request_params' field.
   * @param value the value to set.
   */
  public void setRequestParams(java.lang.CharSequence value) {
    this.request_params = value;
  }

  /**
   * Gets the value of the 'verify_status' field.
   * @return The value of the 'verify_status' field.
   */
  public java.lang.Integer getVerifyStatus() {
    return verify_status;
  }

  /**
   * Sets the value of the 'verify_status' field.
   * @param value the value to set.
   */
  public void setVerifyStatus(java.lang.Integer value) {
    this.verify_status = value;
  }

  /**
   * Gets the value of the 'contract_memo' field.
   * @return The value of the 'contract_memo' field.
   */
  public java.lang.CharSequence getContractMemo() {
    return contract_memo;
  }

  /**
   * Sets the value of the 'contract_memo' field.
   * @param value the value to set.
   */
  public void setContractMemo(java.lang.CharSequence value) {
    this.contract_memo = value;
  }

  /**
   * Gets the value of the 'merchant_sn' field.
   * @return The value of the 'merchant_sn' field.
   */
  public java.lang.CharSequence getMerchantSn() {
    return merchant_sn;
  }

  /**
   * Sets the value of the 'merchant_sn' field.
   * @param value the value to set.
   */
  public void setMerchantSn(java.lang.CharSequence value) {
    this.merchant_sn = value;
  }

  /**
   * Gets the value of the 'contract_id' field.
   * @return The value of the 'contract_id' field.
   */
  public java.lang.CharSequence getContractId() {
    return contract_id;
  }

  /**
   * Sets the value of the 'contract_id' field.
   * @param value the value to set.
   */
  public void setContractId(java.lang.CharSequence value) {
    this.contract_id = value;
  }

  /**
   * Gets the value of the 'mtime' field.
   * @return The value of the 'mtime' field.
   */
  public java.lang.Long getMtime() {
    return mtime;
  }

  /**
   * Sets the value of the 'mtime' field.
   * @param value the value to set.
   */
  public void setMtime(java.lang.Long value) {
    this.mtime = value;
  }

  /**
   * Creates a new MerchantBankPreAvro RecordBuilder.
   * @return A new MerchantBankPreAvro RecordBuilder
   */
  public static com.wosai.upay.merchant.contract.avro.MerchantBankPreAvro.Builder newBuilder() {
    return new com.wosai.upay.merchant.contract.avro.MerchantBankPreAvro.Builder();
  }

  /**
   * Creates a new MerchantBankPreAvro RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new MerchantBankPreAvro RecordBuilder
   */
  public static com.wosai.upay.merchant.contract.avro.MerchantBankPreAvro.Builder newBuilder(com.wosai.upay.merchant.contract.avro.MerchantBankPreAvro.Builder other) {
    return new com.wosai.upay.merchant.contract.avro.MerchantBankPreAvro.Builder(other);
  }

  /**
   * Creates a new MerchantBankPreAvro RecordBuilder by copying an existing MerchantBankPreAvro instance.
   * @param other The existing instance to copy.
   * @return A new MerchantBankPreAvro RecordBuilder
   */
  public static com.wosai.upay.merchant.contract.avro.MerchantBankPreAvro.Builder newBuilder(com.wosai.upay.merchant.contract.avro.MerchantBankPreAvro other) {
    return new com.wosai.upay.merchant.contract.avro.MerchantBankPreAvro.Builder(other);
  }

  /**
   * RecordBuilder for MerchantBankPreAvro instances.
   */
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<MerchantBankPreAvro>
    implements org.apache.avro.data.RecordBuilder<MerchantBankPreAvro> {

    private java.lang.CharSequence id;
    private java.lang.CharSequence merchant_id;
    private java.lang.CharSequence request_params;
    private int verify_status;
    private java.lang.CharSequence contract_memo;
    private java.lang.CharSequence merchant_sn;
    private java.lang.CharSequence contract_id;
    private long mtime;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(com.wosai.upay.merchant.contract.avro.MerchantBankPreAvro.Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.id)) {
        this.id = data().deepCopy(fields()[0].schema(), other.id);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[1].schema(), other.merchant_id);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.request_params)) {
        this.request_params = data().deepCopy(fields()[2].schema(), other.request_params);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.verify_status)) {
        this.verify_status = data().deepCopy(fields()[3].schema(), other.verify_status);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.contract_memo)) {
        this.contract_memo = data().deepCopy(fields()[4].schema(), other.contract_memo);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.merchant_sn)) {
        this.merchant_sn = data().deepCopy(fields()[5].schema(), other.merchant_sn);
        fieldSetFlags()[5] = true;
      }
      if (isValidValue(fields()[6], other.contract_id)) {
        this.contract_id = data().deepCopy(fields()[6].schema(), other.contract_id);
        fieldSetFlags()[6] = true;
      }
      if (isValidValue(fields()[7], other.mtime)) {
        this.mtime = data().deepCopy(fields()[7].schema(), other.mtime);
        fieldSetFlags()[7] = true;
      }
    }

    /**
     * Creates a Builder by copying an existing MerchantBankPreAvro instance
     * @param other The existing instance to copy.
     */
    private Builder(com.wosai.upay.merchant.contract.avro.MerchantBankPreAvro other) {
            super(SCHEMA$);
      if (isValidValue(fields()[0], other.id)) {
        this.id = data().deepCopy(fields()[0].schema(), other.id);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[1].schema(), other.merchant_id);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.request_params)) {
        this.request_params = data().deepCopy(fields()[2].schema(), other.request_params);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.verify_status)) {
        this.verify_status = data().deepCopy(fields()[3].schema(), other.verify_status);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.contract_memo)) {
        this.contract_memo = data().deepCopy(fields()[4].schema(), other.contract_memo);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.merchant_sn)) {
        this.merchant_sn = data().deepCopy(fields()[5].schema(), other.merchant_sn);
        fieldSetFlags()[5] = true;
      }
      if (isValidValue(fields()[6], other.contract_id)) {
        this.contract_id = data().deepCopy(fields()[6].schema(), other.contract_id);
        fieldSetFlags()[6] = true;
      }
      if (isValidValue(fields()[7], other.mtime)) {
        this.mtime = data().deepCopy(fields()[7].schema(), other.mtime);
        fieldSetFlags()[7] = true;
      }
    }

    /**
      * Gets the value of the 'id' field.
      * @return The value.
      */
    public java.lang.CharSequence getId() {
      return id;
    }

    /**
      * Sets the value of the 'id' field.
      * @param value The value of 'id'.
      * @return This builder.
      */
    public com.wosai.upay.merchant.contract.avro.MerchantBankPreAvro.Builder setId(java.lang.CharSequence value) {
      validate(fields()[0], value);
      this.id = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'id' field has been set.
      * @return True if the 'id' field has been set, false otherwise.
      */
    public boolean hasId() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'id' field.
      * @return This builder.
      */
    public com.wosai.upay.merchant.contract.avro.MerchantBankPreAvro.Builder clearId() {
      id = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'merchant_id' field.
      * @return The value.
      */
    public java.lang.CharSequence getMerchantId() {
      return merchant_id;
    }

    /**
      * Sets the value of the 'merchant_id' field.
      * @param value The value of 'merchant_id'.
      * @return This builder.
      */
    public com.wosai.upay.merchant.contract.avro.MerchantBankPreAvro.Builder setMerchantId(java.lang.CharSequence value) {
      validate(fields()[1], value);
      this.merchant_id = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_id' field has been set.
      * @return True if the 'merchant_id' field has been set, false otherwise.
      */
    public boolean hasMerchantId() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'merchant_id' field.
      * @return This builder.
      */
    public com.wosai.upay.merchant.contract.avro.MerchantBankPreAvro.Builder clearMerchantId() {
      merchant_id = null;
      fieldSetFlags()[1] = false;
      return this;
    }

    /**
      * Gets the value of the 'request_params' field.
      * @return The value.
      */
    public java.lang.CharSequence getRequestParams() {
      return request_params;
    }

    /**
      * Sets the value of the 'request_params' field.
      * @param value The value of 'request_params'.
      * @return This builder.
      */
    public com.wosai.upay.merchant.contract.avro.MerchantBankPreAvro.Builder setRequestParams(java.lang.CharSequence value) {
      validate(fields()[2], value);
      this.request_params = value;
      fieldSetFlags()[2] = true;
      return this;
    }

    /**
      * Checks whether the 'request_params' field has been set.
      * @return True if the 'request_params' field has been set, false otherwise.
      */
    public boolean hasRequestParams() {
      return fieldSetFlags()[2];
    }


    /**
      * Clears the value of the 'request_params' field.
      * @return This builder.
      */
    public com.wosai.upay.merchant.contract.avro.MerchantBankPreAvro.Builder clearRequestParams() {
      request_params = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    /**
      * Gets the value of the 'verify_status' field.
      * @return The value.
      */
    public java.lang.Integer getVerifyStatus() {
      return verify_status;
    }

    /**
      * Sets the value of the 'verify_status' field.
      * @param value The value of 'verify_status'.
      * @return This builder.
      */
    public com.wosai.upay.merchant.contract.avro.MerchantBankPreAvro.Builder setVerifyStatus(int value) {
      validate(fields()[3], value);
      this.verify_status = value;
      fieldSetFlags()[3] = true;
      return this;
    }

    /**
      * Checks whether the 'verify_status' field has been set.
      * @return True if the 'verify_status' field has been set, false otherwise.
      */
    public boolean hasVerifyStatus() {
      return fieldSetFlags()[3];
    }


    /**
      * Clears the value of the 'verify_status' field.
      * @return This builder.
      */
    public com.wosai.upay.merchant.contract.avro.MerchantBankPreAvro.Builder clearVerifyStatus() {
      fieldSetFlags()[3] = false;
      return this;
    }

    /**
      * Gets the value of the 'contract_memo' field.
      * @return The value.
      */
    public java.lang.CharSequence getContractMemo() {
      return contract_memo;
    }

    /**
      * Sets the value of the 'contract_memo' field.
      * @param value The value of 'contract_memo'.
      * @return This builder.
      */
    public com.wosai.upay.merchant.contract.avro.MerchantBankPreAvro.Builder setContractMemo(java.lang.CharSequence value) {
      validate(fields()[4], value);
      this.contract_memo = value;
      fieldSetFlags()[4] = true;
      return this;
    }

    /**
      * Checks whether the 'contract_memo' field has been set.
      * @return True if the 'contract_memo' field has been set, false otherwise.
      */
    public boolean hasContractMemo() {
      return fieldSetFlags()[4];
    }


    /**
      * Clears the value of the 'contract_memo' field.
      * @return This builder.
      */
    public com.wosai.upay.merchant.contract.avro.MerchantBankPreAvro.Builder clearContractMemo() {
      contract_memo = null;
      fieldSetFlags()[4] = false;
      return this;
    }

    /**
      * Gets the value of the 'merchant_sn' field.
      * @return The value.
      */
    public java.lang.CharSequence getMerchantSn() {
      return merchant_sn;
    }

    /**
      * Sets the value of the 'merchant_sn' field.
      * @param value The value of 'merchant_sn'.
      * @return This builder.
      */
    public com.wosai.upay.merchant.contract.avro.MerchantBankPreAvro.Builder setMerchantSn(java.lang.CharSequence value) {
      validate(fields()[5], value);
      this.merchant_sn = value;
      fieldSetFlags()[5] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_sn' field has been set.
      * @return True if the 'merchant_sn' field has been set, false otherwise.
      */
    public boolean hasMerchantSn() {
      return fieldSetFlags()[5];
    }


    /**
      * Clears the value of the 'merchant_sn' field.
      * @return This builder.
      */
    public com.wosai.upay.merchant.contract.avro.MerchantBankPreAvro.Builder clearMerchantSn() {
      merchant_sn = null;
      fieldSetFlags()[5] = false;
      return this;
    }

    /**
      * Gets the value of the 'contract_id' field.
      * @return The value.
      */
    public java.lang.CharSequence getContractId() {
      return contract_id;
    }

    /**
      * Sets the value of the 'contract_id' field.
      * @param value The value of 'contract_id'.
      * @return This builder.
      */
    public com.wosai.upay.merchant.contract.avro.MerchantBankPreAvro.Builder setContractId(java.lang.CharSequence value) {
      validate(fields()[6], value);
      this.contract_id = value;
      fieldSetFlags()[6] = true;
      return this;
    }

    /**
      * Checks whether the 'contract_id' field has been set.
      * @return True if the 'contract_id' field has been set, false otherwise.
      */
    public boolean hasContractId() {
      return fieldSetFlags()[6];
    }


    /**
      * Clears the value of the 'contract_id' field.
      * @return This builder.
      */
    public com.wosai.upay.merchant.contract.avro.MerchantBankPreAvro.Builder clearContractId() {
      contract_id = null;
      fieldSetFlags()[6] = false;
      return this;
    }

    /**
      * Gets the value of the 'mtime' field.
      * @return The value.
      */
    public java.lang.Long getMtime() {
      return mtime;
    }

    /**
      * Sets the value of the 'mtime' field.
      * @param value The value of 'mtime'.
      * @return This builder.
      */
    public com.wosai.upay.merchant.contract.avro.MerchantBankPreAvro.Builder setMtime(long value) {
      validate(fields()[7], value);
      this.mtime = value;
      fieldSetFlags()[7] = true;
      return this;
    }

    /**
      * Checks whether the 'mtime' field has been set.
      * @return True if the 'mtime' field has been set, false otherwise.
      */
    public boolean hasMtime() {
      return fieldSetFlags()[7];
    }


    /**
      * Clears the value of the 'mtime' field.
      * @return This builder.
      */
    public com.wosai.upay.merchant.contract.avro.MerchantBankPreAvro.Builder clearMtime() {
      fieldSetFlags()[7] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public MerchantBankPreAvro build() {
      try {
        MerchantBankPreAvro record = new MerchantBankPreAvro();
        record.id = fieldSetFlags()[0] ? this.id : (java.lang.CharSequence) defaultValue(fields()[0]);
        record.merchant_id = fieldSetFlags()[1] ? this.merchant_id : (java.lang.CharSequence) defaultValue(fields()[1]);
        record.request_params = fieldSetFlags()[2] ? this.request_params : (java.lang.CharSequence) defaultValue(fields()[2]);
        record.verify_status = fieldSetFlags()[3] ? this.verify_status : (java.lang.Integer) defaultValue(fields()[3]);
        record.contract_memo = fieldSetFlags()[4] ? this.contract_memo : (java.lang.CharSequence) defaultValue(fields()[4]);
        record.merchant_sn = fieldSetFlags()[5] ? this.merchant_sn : (java.lang.CharSequence) defaultValue(fields()[5]);
        record.contract_id = fieldSetFlags()[6] ? this.contract_id : (java.lang.CharSequence) defaultValue(fields()[6]);
        record.mtime = fieldSetFlags()[7] ? this.mtime : (java.lang.Long) defaultValue(fields()[7]);
        return record;
      } catch (java.lang.Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<MerchantBankPreAvro>
    WRITER$ = (org.apache.avro.io.DatumWriter<MerchantBankPreAvro>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<MerchantBankPreAvro>
    READER$ = (org.apache.avro.io.DatumReader<MerchantBankPreAvro>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

}
