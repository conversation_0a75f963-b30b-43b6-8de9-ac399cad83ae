package com.wosai.upay.merchant.contract.model.enums;


import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * D0开通类型枚举类型枚举
 *
 * <AUTHOR>
 */
public enum OpenDayZeroTypeEnum implements ITextValueEnum<String> {

    CLOSE("0", "关闭"),

    OPEN("1", "开通");

    private final String value;
    private final String text;

    OpenDayZeroTypeEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public String getValue() {
        return this.value;
    }
}