package com.wosai.upay.merchant.contract.utils.guotong;

import com.wosai.mpay.util.Digest;
import sun.misc.BASE64Decoder;

import javax.crypto.Cipher;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @date 2024/12/11
 */
public class GuotongSignUtil {

    public static String sign(TreeMap<String, Object> params, String publicKey) {
        try {
            // 拼接参数，生成sign
            StringBuilder sb = new StringBuilder();
            for (String key : params.keySet()) {
                sb.append(key).append("=").append(params.get(key)).append("&");
            }
            String res = sb.substring(0, sb.lastIndexOf("&"));
            String sha256 = Digest.sha256(res.getBytes(StandardCharsets.UTF_8));
            return encrypt(publicKey, sha256);
        } catch (Exception e) {
            throw new RuntimeException("sign error");
        }
    }


    /**
     * 使用公钥对明文进行签名
     *
     * @param publicKey 公钥
     * @param plainText 明文
     * @return
     */
    public static String encrypt(String publicKey, String plainText) {
        try {
            Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.ENCRYPT_MODE, getPublicKey(publicKey));

            byte[] inputBytes = plainText.getBytes(StandardCharsets.UTF_8);

            // RSA加密块大小限制为117字节
            int maxBlockSize = 117;
            int inputLength = inputBytes.length;

            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                for (int offset = 0; offset < inputLength; offset += maxBlockSize) {
                    int blockSize = Math.min(maxBlockSize, inputLength - offset);
                    byte[] encryptedBlock = cipher.doFinal(inputBytes, offset, blockSize);
                    outputStream.write(encryptedBlock);
                }

                return GuotongBase64Util.encode(outputStream.toByteArray());
            }
        } catch (Exception e) {
            throw new RuntimeException("RSA encryption failed", e);
        }
    }


    /**
     * 得到公钥
     *
     * @param key 密钥字符串（经过base64编码�?
     * @throws Exception
     */
    public static PublicKey getPublicKey(String key) throws Exception {
        byte[] keyBytes;
        keyBytes = new BASE64Decoder().decodeBuffer(key);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePublic(keySpec);
    }

}
