package com.wosai.upay.merchant.contract.utils;

import com.wosai.upay.merchant.contract.exception.ContractBizException;
import net.coobird.thumbnailator.Thumbnails;
import net.coobird.thumbnailator.tasks.UnsupportedFormatException;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import sun.misc.BASE64Encoder;
import tk.mybatis.mapper.util.StringUtil;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;

/**
 * 图片处理工具类
 *
 * <AUTHOR>
 * @date 2019/4/15 3:57 PM
 */
public class ImageUtil {

    private static final Logger log = LoggerFactory.getLogger(ImageUtil.class);

    public static String encodeImageToApacheBase64(String url, int imageMaxSize) {
        ByteArrayOutputStream outputStream;
        try {
            URL imageUrl = new URL(url);
            HttpURLConnection connection = (HttpURLConnection) imageUrl.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5 * 1000);
            outputStream = inputStreamToByteArrayOutputStream(connection.getInputStream());
        } catch (Exception e) {
            log.error("获取图片异常:{}", url, e);
            throw new RuntimeException(String.format("图片获取异常:%s", url));
        }
        try {
            return new String(Base64.encodeBase64(condensePic(outputStream, imageMaxSize)));
        } catch (UnsupportedFormatException e) {
            throw new ContractBizException(e.toString());
        } catch (Exception e) {
            log.error("图片压缩异常:{}", url, e);
            throw new RuntimeException(String.format("图片压缩异常:%s", url));
        }
    }

    /**
     * 将网络图片编码为base64
     *
     * @param url          图片URL
     * @param imageMaxSize 图片最大大小（单位：字节），超过就压缩图片质量
     * @return Base64编码的图片
     */
    public static String encodeImageToBase64(String url, int imageMaxSize) throws Exception {

        URL imageUrl = new URL(url);

        //将图片文件转化为字节数组字符串，并对其进行Base64编码处理
        //打开链接
        HttpURLConnection conn = null;
        InputStream inStream = null;
        InputStream inputStreamForImageCompress = null; // 用于图像压缩的字节输入流
        ByteArrayOutputStream outStream = null;
        try {
            conn = (HttpURLConnection) imageUrl.openConnection();
            // 设置请求方式为"GET"
            conn.setRequestMethod("GET");
            // 超时响应时间为5秒
            conn.setConnectTimeout(5 * 1000);
            // 通过输入流获取图片数据
            inStream = conn.getInputStream();
            // 得到图片的二进制数据，以二进制封装得到数据，具有通用性
            outStream = inputStreamToByteArrayOutputStream(inStream);
            byte[] data = condensePic(outStream, imageMaxSize);
            BASE64Encoder encoder = new BASE64Encoder();
            String result = encoder.encode(data);
            if (result == null || result.length() <= 0) {
                throw new Exception("将网络图片: " + url + " 编码为base64格式后为空");
            }

            log.info("将网络图片: {} 编码为base64格式的长度为: {}", url, result.length());
            return result;
        } catch (IOException e) {
            log.error("", e);
            throw new IOException("网络图片转Base64编码失败");
        } finally {
            if (conn != null) {
                conn.disconnect();
            }
            if (inStream != null) {
                inStream.close();
            }
            if (inputStreamForImageCompress != null) {
                inputStreamForImageCompress.close();
            }
            if (outStream != null) {
                outStream.close();
            }
        }
    }


    public static byte[] condensePic(ByteArrayOutputStream outputStream, int imageMaxSize) throws IOException {
        byte[] data = outputStream.toByteArray();
        if (data.length > imageMaxSize) {
            outputStream.reset();
            ByteArrayInputStream inputStream = new ByteArrayInputStream(data);
            //quality 并不是 原有文件大小/现有文件大小 的意思 有自己的计算方法
            Thumbnails.of(inputStream).scale(0.5D).outputQuality(0.7f).toOutputStream(outputStream);
            return outputStream.toByteArray();
        } else {
            return data;
        }
    }

    public static void main(String[] args) throws Exception {
//        String url = "https://wosai-images.oss-cn-hangzhou.aliyuncs.com/00/2d282c26e5086f798a6a167c435e8b.jpeg";
//        String url2 = "https://private-images.shouqianba.com/sales-system-gateway/2021-05-25/926ec5b3c3a248078772e0a694e91fc5.jpeg?Expires=**********&OSSAccessKeyId=LTAIf4w9oUgn1uKg&Signature=X%2FCu7ZwJmQQxvBgoqVl06jeIiyE%3D&x-oss-process=image%2Fwatermark%2Ccolor_FFFFAA%2Csize_20%2Ctext_5pS26ZKx5ZCn5o6M5p-cMjAyMS0wNS0yNSAxNDowNzo1OS4wNTk";
////        URL imageUrl = new URL(url);
////        System.out.println();
////        encodeImageToApacheBase64(compulsory(url), 8000);
////        encodeImageToApacheBase64(url, 8000);
////        System.out.println(getSuffix(url, LklPicTypeV3.BANK_CARD));
////        System.out.println(getSuffix(url2, LklPicTypeV3.BANK_CARD));
//
////        String conUrl = "https://wosai-images.oss-cn-hangzhou.aliyuncs.com/00/2d282c26e5086f798a6a167c435e8b.jpeg?x-oss-process=image/format,png";
//        String u1 = "https://wosai-images.oss-cn-hangzhou.aliyuncs.com/00/2d282c26e5086f798a6a167c435e8b.jpeg?x-oss-process=image/format,heic/resize,l_900,h_600";
//        encodeImageToApacheBase64(u1, 8000);

        String url = "https://wosai-images.oss-cn-hangzhou.aliyuncs.com/00/2d282c26e5086f798a6a167c435e8b.jpeg";
//        encodeImageToApacheBase64(url, 8000);   //3563118 riff
        String complusoryUrl = compulsory(url);
//        https://wosai-images.oss-cn-hangzhou.aliyuncs.com/00/2d282c26e5086f798a6a167c435e8b.jpeg?x-oss-process=image/format,png
        encodeImageToApacheBase64(complusoryUrl, 8000);     //******** png
        String jpgUrl = "https://wosai-images.oss-cn-hangzhou.aliyuncs.com/00/2d282c26e5086f798a6a167c435e8b.jpeg?x-oss-process=image/format,jpg";
//        encodeImageToApacheBase64(jpgUrl, 8000);    //4243440   jfif
        String jpegUrl = "https://wosai-images.oss-cn-hangzhou.aliyuncs.com/00/2d282c26e5086f798a6a167c435e8b.jpeg?x-oss-process=image/format,jpeg";
//        encodeImageToApacheBase64(jpegUrl, 8000);   //4243440 jfif

        String typeUrl4 = "https://wosai-images.oss-cn-hangzhou.aliyuncs.com/00/2d282c26e5086f798a6a167c435e8b.jpeg?x-oss-process=image/resize,p_90/format,png";
//        encodeImageToApacheBase64(typeUrl4, 8000);

        String typeUrl3 = "https://wosai-images.oss-cn-hangzhou.aliyuncs.com/00/2d282c26e5086f798a6a167c435e8b.jpeg?x-oss-process=image/resize,p_60/format,png";
//        encodeImageToApacheBase64(typeUrl3, 8000);  //6855425

        String typeUrl = "https://wosai-images.oss-cn-hangzhou.aliyuncs.com/00/2d282c26e5086f798a6a167c435e8b.jpeg?x-oss-process=image/resize,p_50/format,png";
//        encodeImageToApacheBase64(typeUrl, 8000);   //5067092 png   2097152

        String typeUrl2 = "https://wosai-images.oss-cn-hangzhou.aliyuncs.com/00/2d282c26e5086f798a6a167c435e8b.jpeg?x-oss-process=image/resize,p_40/format,png";
//        encodeImageToApacheBase64(typeUrl2, 8000);  //3039141 png

        String typeUrl6 = "https://wosai-images.oss-cn-hangzhou.aliyuncs.com/00/2d282c26e5086f798a6a167c435e8b.jpeg?x-oss-process=image/resize,p_10/format,png";
//        encodeImageToApacheBase64(typeUrl6, 8000);  //190041 png


    }

    public static ByteArrayOutputStream inputStreamToByteArrayOutputStream(InputStream inputStream) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        // 创建一个Buffer字符串
        byte[] buffer = new byte[1024];
        // 每次读取的字符串长度，如果为-1，代表全部读取完毕
        int length;
        // 使用一个输入流从buffer里把数据读取出来
        while ((length = inputStream.read(buffer)) != -1) {
            // 用输出流往buffer里写入数据，中间参数代表从哪个位置开始读，len代表读取的长度
            outputStream.write(buffer, 0, length);
        }

        return outputStream;
    }

    /**
     * 将图片 url 转成合法格式
     *
     * @param url
     * @return
     */
    public static String convertUrl(String url) {
        if (StringUtil.isEmpty(url) || "null".equalsIgnoreCase(url) || ",".equalsIgnoreCase(url)) {
            return null;
        }
        return url;
    }

    /**
     * 获取 图片 的类型
     *
     * @param picUrl
     * @return
     */
    public static String getSuffix(String picUrl) {
        if (StringUtil.isEmpty(picUrl) || "null".equalsIgnoreCase(picUrl) || ",".equalsIgnoreCase(picUrl)) {
            throw new ContractBizException(picUrl + " 图片为空");
        }
        if (picUrl.contains("x-oss-process")) {
            picUrl = picUrl.substring(0, picUrl.indexOf("x-oss-process") - 1);
        }
        if (picUrl.contains("?")) {
            picUrl = picUrl.substring(0, picUrl.lastIndexOf("?"));
        }
        String type = FilenameUtils.getExtension(picUrl).toLowerCase();
        return StringUtil.isEmpty(type) ? "jpg" : type;
    }

    /**
     * 将图片强制转换成jpg格式
     *
     * @param picUrl
     * @return
     */
    public static String compulsory(String picUrl) {
        if (StringUtil.isEmpty(picUrl) || "null".equalsIgnoreCase(picUrl) || ",".equalsIgnoreCase(picUrl)) {
            throw new ContractBizException("图片为空");
        }
        if (picUrl.contains("x-oss-process")) {
            picUrl = picUrl + "/format,png";
        } else {
            picUrl = picUrl + "?x-oss-process=image/format,png";
        }
        return picUrl;
    }

    public static String scaleOss(String picUrl, int scale) {
        if (StringUtil.isEmpty(picUrl) || "null".equalsIgnoreCase(picUrl) || ",".equalsIgnoreCase(picUrl)) {
            throw new ContractBizException("图片为空");
        }
        if (picUrl.contains("x-oss-process")) {

        } else {
            return String.format("%s?x-oss-process=image/resize,p_%d", picUrl, scale);
        }
        return null;
    }

    /**
     * 解析url中 存在多条路径的情况
     *
     * @param urls
     * @return
     */
    public static List<String> getUrls(String urls) {
        if (StringUtil.isEmpty(urls) && !"null".equalsIgnoreCase(urls)) {
            return null;
        }
        String[] split = urls.split(",");
        ArrayList<String> list = new ArrayList<>();
        for (String url : split) {
            if (StringUtil.isNotEmpty(url) && url.startsWith("http")){
                list.add(url);
            }
        }
        return list;
    }

}
