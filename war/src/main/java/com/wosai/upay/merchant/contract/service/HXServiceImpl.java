package com.wosai.upay.merchant.contract.service;

import avro.shaded.com.google.common.collect.Maps;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.merchant.contract.biz.HXBiz;
import com.wosai.upay.merchant.contract.config.ApolloParamsConfig;
import com.wosai.upay.merchant.contract.constant.hx.HXConstant;
import com.wosai.upay.merchant.contract.converter.hx.HXConverter;
import com.wosai.upay.merchant.contract.converter.hx.HXMerchantConverter;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.exception.ContractException;
import com.wosai.upay.merchant.contract.exception.ContractSysException;
import com.wosai.upay.merchant.contract.model.*;
import com.wosai.upay.merchant.contract.model.huaxia.request.*;
import com.wosai.upay.merchant.contract.model.huaxia.response.*;
import com.wosai.upay.merchant.contract.model.provider.HXParam;
import com.wosai.upay.merchant.contract.model.terminal.AddTermInfoDTO;
import com.wosai.upay.merchant.contract.model.terminal.LogOutTermInfoDTO;
import com.wosai.upay.merchant.contract.model.terminal.UpdateTermInfoDTO;
import com.wosai.upay.merchant.contract.model.weixin.SubdevConfigResp;
import com.wosai.upay.merchant.contract.utils.SnowFlakeIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.wosai.upay.merchant.contract.constant.Constant.*;
import static com.wosai.upay.merchant.contract.constant.hx.HXConstant.BIZ_ERROR;
import static com.wosai.upay.merchant.contract.converter.hx.HXConverter.queryMerAddStatus;
import static com.wosai.upay.merchant.contract.converter.hx.HXConverter.wechatQuery;

/**
 * @Description:
 * <AUTHOR>
 * @Date: 2021/11/1 10:09 上午
 */
@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class HXServiceImpl implements HXService {
    @Autowired
    private HXBiz hxBiz;
    @Autowired
    private HXMerchantConverter hxMerchantConverter;
    @Autowired
    private ApolloParamsConfig apolloParamsConfig;


    /**
     * 商户进件
     *
     * @param contextParam 上下文
     * @param hxParam      渠道参数
     * @return
     */
    @Override
    public ContractResponse contractMerchant(Map contextParam, HXParam hxParam) {
        ContractResponse response = new ContractResponse();
        Map<String, Object> tradeParams = Maps.newHashMap();
        //华夏请求参数
        HXMerchantAdd hxMerchantAdd = null;
        //华夏返回参数
        HXBaseResponse hxMerchantAddRes = null;

        //商户号
        String merchantSn = WosaiMapUtils.getString(WosaiMapUtils.getMap(contextParam, "merchant"), Merchant.SN);
        try {
            //组装请求参数
            hxMerchantAdd = hxMerchantConverter.buildMerchantAdd(contextParam, hxParam);
            //请求接口
            hxMerchantAddRes = hxBiz.merchantAdd(hxMerchantAdd, hxParam);
            //返回参数判断
            if (HXConstant.SUCCESS.equals(hxMerchantAddRes.getRespCode())) {
                tradeParams.put("contractId", hxMerchantAdd.getId());
                response.setTradeParam(tradeParams);
                response.setCode(RESULT_CODE_SUCCESSS);
                response.setMessage("商户入网审核中");
            } else {
                //业务异常
                if (apolloParamsConfig.getString(ApolloConfigParams.HX_ERROR_CODE, BIZ_ERROR).contains(hxMerchantAddRes.getRespCode())) {
                    throw new ContractBizException(hxMerchantAddRes.getRespMsg());
                } else {
                    //系统异常
                    throw new ContractSysException(hxMerchantAddRes.getRespMsg());
                }
            }
        } catch (ContractException e) {
            log.error("华夏新增商户异常:商户号：{}", merchantSn, e);
            response.setCode(Objects.isNull(e.getCode()) ? RESULT_OLD_CODE : e.getCode());
            response.setMessage(e.getMessage());
        } catch (Exception e) {
            log.error("华夏新增商户系统异常:商户号：{}", merchantSn, e);
            response.setCode(RESULT_CODE_SYSTEM_EXCEPTION);
            response.setMessage(e.getMessage());
        }
        response.setRequestParam(JSONObject.parseObject(JSONObject.toJSONString(hxMerchantAdd), Map.class));
        response.setResponseParam(JSONObject.parseObject(JSONObject.toJSONString(hxMerchantAddRes), Map.class));
        return response;
    }

    /**
     * 其他支付方式进件
     *
     * @param contractSubTask
     * @param hxParam
     * @return
     */
    @Override
    public ContractResponse contractMerchantOtherPayWay(ContractSubTask contractSubTask, HXParam hxParam) {
        return hxBiz.contractOtherPayWay(contractSubTask, hxParam);
    }

    @Override
    public Map wechatSubDevConfig(WeixinConfig weixinConfig, HXParam hxParam) {
        //微信商户号
        List<String> errorMessage = new ArrayList<>();
        //微信子商户号
        String payMerchantNo = weixinConfig.getWeixinMchId();
        //华夏银行商户号 HX开头的
        String hxMerNo;
        String hxAppId;
        try {
            hxMerNo = hxBiz.getHxMerNo(payMerchantNo);
            hxAppId = hxBiz.getHxAppIdByHxMerNo(hxMerNo);
        } catch (ContractBizException exception) {
            log.error("根据微信子商户获取华夏商户号/appId失败:{}", exception);
            errorMessage.add(exception.getMessage());
            return CollectionUtil.hashMap(KEY_RESULT_CODE, RESULT_CODE_FAIL, KEY_RESULT_MESSAGE, errorMessage);
        }
        List<WeixinAppidConfig> appidConfigs = weixinConfig.getAppidConfigs();
        //1.配置支付目录
        if (WosaiCollectionUtils.isNotEmpty(weixinConfig.getPayAuthPath())) {
            for (String path : weixinConfig.getPayAuthPath()) {
                HXWechatSubPayPath subPayPath = HXConverter.wechatPayPath(hxMerNo, path);
                subPayPath.setParentServerorgno(apolloParamsConfig.getString("hx-parentServerorgno", ""));
                HXWechatSubAddResponse resp = hxBiz.wechatAddPayPath(subPayPath, hxParam, hxAppId);
                if (!HXConstant.SUCCESS.equals(resp.getRespCode()) && !resp.getRespMsg().contains("重复")) {
                    errorMessage.add(resp.getRespMsg());
                }
            }
        }
        //2.配置交易参数
        if (WosaiCollectionUtils.isNotEmpty(appidConfigs)) {
            for (WeixinAppidConfig config : appidConfigs) {
                //appid
                if (WosaiStringUtils.isNotEmpty(config.getSub_appid())) {
                    HXWechatSubAppID request = HXConverter.wechatAppId(hxMerNo, config.getSub_appid());
                    request.setParentServerorgno(apolloParamsConfig.getString("hx-parentServerorgno", ""));
                    HXWechatSubAddResponse resp = hxBiz.wechatAddAppId(request, hxParam, hxAppId);
                    if (!HXConstant.SUCCESS.equals(resp.getRespCode()) && !resp.getRespMsg().contains("重复")) {
                        errorMessage.add(resp.getRespMsg());
                    }
                }
            }
        }
        //返回结果判断 有错误信息返回失败
        if (errorMessage.size() > 0) {
            log.error("调用华夏微信子商户配置接口失败,payMerchantNo:{},错误信息：{}", hxMerNo, JSONObject.toJSON(errorMessage));
            return CollectionUtil.hashMap(KEY_RESULT_CODE, RESULT_CODE_FAIL, KEY_RESULT_MESSAGE, errorMessage);
        } else {
            return CollectionUtil.hashMap(KEY_RESULT_CODE, RESULT_CODE_SUCCESS, KEY_RESULT_MESSAGE, "配置成功");
        }
    }

    /**
     * 查询
     *
     * @param payMerchantNo 华夏商户号 HX开头的
     * @param hxParam
     * @return
     */
    @Override
    public SubdevConfigResp queryWechatSubDevConfig(String payMerchantNo, HXParam hxParam) {
        try {
            final String hxMerNo = hxBiz.getHxMerNo(payMerchantNo);
            final String hxAppId = hxBiz.getHxAppIdByHxMerNo(hxMerNo);
            HXWechatSubQuery request = wechatQuery(hxMerNo);
            request.setParentServerorgno(apolloParamsConfig.getString("hx-parentServerorgno", ""));
            HXWechatSubQueryResponse response = hxBiz.wechatConfigQuery(request, hxParam, hxAppId);
            //查询成功
            if (HXConstant.SUCCESS.equals(response.getRespCode())) {
                SubdevConfigResp resp = new SubdevConfigResp();
                //支付目录
                String pathList = response.getJsapiPathList();
                if (StringUtils.isNotBlank(pathList)) {
                    String[] split = pathList.split(",");
                    List<String> path = new ArrayList<>(Arrays.asList(split));
                    resp.setJsapi_path_list(path);
                }
                //appID
                String configList = response.getAppidConfigList();
                if (StringUtils.isNotBlank(configList)) {
                    String[] split = configList.split(",");
                    List<SubdevConfigResp.AppidConfig> configs = new ArrayList<>();
                    for (String appId : split) {
                        SubdevConfigResp.AppidConfig config = new SubdevConfigResp.AppidConfig();
                        config.setSub_appid(appId);
                        configs.add(config);
                    }
                    resp.setAppid_config_list(configs);
                }
                return resp;
            } else {
                //查询返回业务异常
                log.error("华夏查询微信子商户配置业务异常:providerId:{},错误信息：{}", payMerchantNo, response.getRespMsg());
                throw new ContractBizException("查询微信appId异常");
            }
        } catch (Exception e) {
            log.error("查询微信appId异常", e);
            throw new ContractBizException("查询微信appId异常");
        }
    }

    @Override
    public ContractResponse queryContractStatusByContractId(ContractSubTask contractSubTask, HXParam hxParam) {
        ContractResponse contractResponse = new ContractResponse();
        try {
            //1,组装参数
            HXMerchantQuery request = queryMerAddStatus(contractSubTask.getContract_id());
            //根据requestBody获取appId
            final String requestBody = contractSubTask.getRequest_body();
            final Map map = JSONObject.parseObject(requestBody, Map.class);
            final String serverOrgNo = (String) map.computeIfAbsent("serverOrgNo", t -> {
                throw new ContractBizException("没有找到serverOrgNo");
            });
            //根据serverOrgNo获取appId
            final String hxAppId = hxBiz.getHxAppIdByServerOrgNo(serverOrgNo);
            //2，调用 商户进件查询接口
            HXMerchantQueryResponse response = hxBiz.queryApplyStatus(request, hxParam, hxAppId);
            //根据类型处理
            contractResponse = hxBiz.handleTaskType(response, contractSubTask, hxParam);
        } catch (ContractException e) {
            log.error("调用华夏查询接口接口业务异常,商户{}", contractSubTask.getMerchant_sn(), e);
            contractResponse.setCode(e.getCode());
            contractResponse.setMessage("调用华夏查询接口接口业务异常" + e.getMessage());
        } catch (Exception e) {
            log.error("调用华夏查询商户进件接口系统异常,商户{}", contractSubTask.getMerchant_sn(), e);
            contractResponse.setCode(500);
            contractResponse.setMessage("华夏查询商户进件接口系统异常");
        }
        return contractResponse;
    }


    @Override
    public ContractResponse addTermInfo(AddTermInfoDTO dto, HXParam hxParam, Integer payWay) {
        final ContractResponse contractResponse = hxBiz.termInfoOperate(dto, hxParam, "I", payWay);
        return contractResponse;
    }

    @Override
    public ContractResponse updateTermInfo(UpdateTermInfoDTO dto, HXParam hxParam, Integer payWay) {
        final ContractResponse contractResponse = hxBiz.termInfoOperate(dto, hxParam, "U", payWay);
        return contractResponse;
    }

    @Override
    public ContractResponse LogOutTermInfo(LogOutTermInfoDTO dto, HXParam hxParam, Integer payWay) {
        final ContractResponse contractResponse = hxBiz.termInfoOperate(dto, hxParam, "D", payWay);
        return contractResponse;
    }

    @Override
    public ContractResponse getTermRegistResult(HXTerminalQuery query, HXParam hxParam) {
        ContractResponse response = new ContractResponse();
        response.setCode(RESULT_CODE_SUCCESSS);
        response.setMessage("已废弃");
        return response;
    }

    @Override
    public ContractResponse addTermInfo(String termId, Integer payWay) {
        final ContractResponse contractResponse = hxBiz.termInfoOperate(termId, "I", payWay);
        return contractResponse;
    }

    @Override
    public ContractResponse getTermRegistResult(String termId) {
        ContractResponse response = new ContractResponse();
        try {
            final HXTerminalQuery hxTerminalQuery = new HXTerminalQuery();
            hxTerminalQuery.setSystemNo(String.valueOf(SnowFlakeIdGenerator.getInstance().nextId()));
            hxTerminalQuery.setTermNo(termId);
            //发送请求
            final String hxAppId = hxBiz.getHxAppIdByServerOrgNo(null);
            final HXTerminalQueryResponse hXTerminalQueryResponse = hxBiz.getTermRegistResult(hxTerminalQuery, hxBiz.getHXParam(), hxAppId);
            response.setRequestParam(JSONObject.parseObject(JSONObject.toJSONString(hxTerminalQuery), Map.class))
                    .setResponseParam(JSONObject.parseObject(JSONObject.toJSONString(hXTerminalQueryResponse), Map.class));
            response = hxBiz.handleResponse(hXTerminalQueryResponse, "华夏终端绑定结果查询", null, response);
        } catch (ContractException e) {
            response.setCode(Objects.isNull(e.getCode()) ? RESULT_OLD_CODE : e.getCode());
            response.setMessage(e.getMessage());
        } catch (Exception e) {
            log.error("华夏系统异常{}", e);
            response.setCode(RESULT_CODE_SYSTEM_EXCEPTION);
            response.setMessage(e.getMessage());
        }
        return response;
    }
}
