package com.wosai.upay.merchant.contract.model.fuyou;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 富友进件请求model
 * http://fundwx.fuiou.com/doc/#/scanentry/api_copy?id=_41-%e5%95%86%e6%88%b7%e4%bf%a1%e6%81%af%e7%99%bb%e8%ae%b0%e6%8e%a5%e5%8f%a3
 * <AUTHOR>
 * @date 2023-09-18
 */
@Data
@Accessors(chain = true)
public class FuyouWeixinConfigRequest {

    /**
     * 唯一流水号，机构自己定义，此字段可辅助拉取报文
     */
    private String trace_no;

    /**
     * 机构号,接入机构在富友的唯一代码
     */
    private String ins_cd;

    /**
     * 二级代理结构号
     */
    private String sub_ins_cd;

    /**
     * 代理商类型(0：一般类，1：绿洲)
     */
    private String agencyType;

    /**
     * 路由标记：为空或者 0 ；默认传1
     * 为当前使用对应渠道微信参数
     */
    private String channelFlag;

    /**
     * 配置列表（参数如下）
     */
    private List configs;

    /**
     * 签名，详见签名算法
     */
    private String sign;


    @Data
    @Accessors(chain = true)
    public static class WeixinConfig {
        /**
         * 富友商户号
         */
        private String mchntCd;

        /**
         * JSAPI 支付授权目录
         */
        private String jsapiPath;

        /**
         * JSAPI 支付授权目录
         */
        private String subAppid;

        /**
         * 子商户推荐关注公众账号 APPID
         */
        private String subscribeAppid;
    }
}
