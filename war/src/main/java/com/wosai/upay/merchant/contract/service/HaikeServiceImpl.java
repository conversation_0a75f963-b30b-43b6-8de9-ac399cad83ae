package com.wosai.upay.merchant.contract.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.StringUtils;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.Constants.TradeConstants;
import com.wosai.upay.merchant.contract.biz.*;
import com.wosai.upay.merchant.contract.config.ApolloParamsConfig;
import com.wosai.upay.merchant.contract.constant.*;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.exception.ContractException;
import com.wosai.upay.merchant.contract.exception.ContractSysException;
import com.wosai.upay.merchant.contract.model.*;
import com.wosai.upay.merchant.contract.model.haike.*;
import com.wosai.upay.merchant.contract.model.provider.HaikeActivityParam;
import com.wosai.upay.merchant.contract.model.provider.HaikeParam;
import com.wosai.upay.merchant.contract.model.provider.UnionAlipayParam;
import com.wosai.upay.merchant.contract.model.provider.UnionWeixinParam;
import com.wosai.upay.merchant.contract.model.terminal.*;
import com.wosai.upay.merchant.contract.model.weixin.*;
import com.wosai.upay.merchant.contract.repository.ProviderTerminalRepository;
import com.wosai.upay.merchant.contract.utils.*;
import com.wosai.upay.merchant.contract.utils.ali.AliCommonUtil;
import com.wosai.upay.merchant.contract.utils.chinaUms.http.HttpClientResultBO;
import com.wosai.upay.merchant.contract.utils.chinaUms.http.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;


/**
 * @Description: 海科通道
 * @Author: haochen
 * @Date: 2023/7/12
 */

@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class HaikeServiceImpl implements HaikeService {

    private static final Logger logger = LoggerFactory.getLogger(HaikeService.class);

    private static ExecutorService threadPool = new ThreadPoolExecutor(1, 1, 100, TimeUnit.SECONDS, new ArrayBlockingQueue<>(1000));

    @Autowired
    private ApolloParamsConfig apolloParamsConfig;

    @Autowired
    private ProviderTradeParamsService providerTradeParamsService;

    @Autowired
    private UnionBiz unionBiz;

    @Autowired
    private NewUnionBiz newUnionBiz;

    @Autowired
    private CommonBiz commonBiz;

    @Autowired
    private ProviderTerminalRepository terminalRepository;

    @Autowired
    private ChatBotUtil chatBotUtil;

    @Autowired
    private ErrorInfoUtil errorInfoUtil;

    @Autowired
    private ContractWith259Biz contractWith259Biz;

    @Value("${haike.merchant.contract}")
    private String haikeContractUrl;

    @Autowired
    private HaikeBiz haikeBiz;

    @Autowired
    private ProviderTerminalBiz providerTerminalBiz;

    @Autowired
    private ProviderTerminalIdBiz providerTerminalIdBiz;

    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    private ContractBaseBiz contractBaseBiz;

    @Autowired
    private ParamContextUtil paramContextUtil;

    @Autowired
    private WeiXinRuleBiz weiXinRuleBiz;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private OSSBase64Converter ossBase64Converter;

    private static final String UNION_CHANNEL_NO = "C1000001";

    /**
     * 商户向收单机构进件
     * copy from com.wosai.upay.merchant.contract.service.HaikeServiceImpl#contractMerchant
     * 移除了校验的逻辑,保存交易参数的逻辑（这部分内容本不应该由该服务做，而是由调用方完成）
     *
     * @param contextParam 上下文参数
     * @param haikeParam   海科参数
     * @return 报备结果
     */
    @Override
    public ContractResponse contractToAcquirer(Map contextParam, HaikeParam haikeParam) {
        Map<String, Object> request = Maps.newHashMap();
        Map<String, Object> response = Maps.newHashMap();
        Map<String, Object> tradeParams = Maps.newHashMap();
        int code = 200;
        String message = "";
        try {
            HaikeMerchantContractRequest haikeMerchantContractRequest = haikeBiz.buildContractRequest(contextParam, haikeParam);
            request = JSON.parseObject(JSON.toJSONString(haikeMerchantContractRequest));
            response = call(haikeContractUrl + "/front-api/merch/aggregation-apply", request, haikeParam);
            String resultCode = BeanUtil.getPropString(response, "result_code");
            String resultMsg = BeanUtil.getPropString(response, "result_msg");
            if ("10000".equals(resultCode)) {
                message = "进件成功";
                String providerTerminalId = providerTerminalIdBiz.getHaiKeProviderTerminalIdForMerchant();
                response.put("terminal_id", providerTerminalId);
            } else {
                code = errorInfoUtil.buildResultCode(ErrorInfoUtil.HAIKE_ERROR_INFO, resultCode);
                message = resultMsg;
            }
        } catch (ContractException e) {
            log.error("haike contractMerchant error", e);
            code = errorInfoUtil.buildResultCode(ErrorInfoUtil.HAIKE_ERROR_INFO, String.valueOf(e.getCode()));
            if (WosaiStringUtils.isEmpty(message)) {
                message = e.getMessage();
            }
        } catch (Exception e) {
            log.error("haike contractMerchant error", e);
            code = Constant.RESULT_CODE_SYSTEM_EXCEPTION;
            message = e.getMessage();
            if (e.getCause() != null) {
                message = e.getCause().getMessage();
            }
        }

        ContractResponse result = new ContractResponse();
        result.setCode(code);
        result.setMessage(message);
        result.setRequestParam(request);
        result.setResponseParam(response);
        result.setTradeParam(tradeParams);
        return result;
    }

    @Override
    public ContractResponse contractMerchant(Map contextParam, HaikeParam haikeParam) {
        Map<String, Object> request = Maps.newHashMap();
        Map<String, Object> response = Maps.newHashMap();
        Map<String, Object> tradeParams = Maps.newHashMap();
        Map<String, Object> merchantProviderParams = Maps.newHashMap();
        int code = 200;
        String message = "";
        //0.重复报备校验
        Map<String, Object> merchant = (Map) contextParam.get("merchant");
        String merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        try {
            merchantProviderParams = providerTradeParamsService.getMerchantProviderParams(merchantSn, haikeParam.getChannel_no(), 0, String.valueOf(MerchantProviderParams.PROVIDER_HAIKE));
            haveNet(tradeParams, merchantProviderParams, contextParam);
            HaikeMerchantContractRequest haikeMerchantContractRequest = haikeBiz.buildContractRequest(contextParam, haikeParam);
            request = JSON.parseObject(JSON.toJSONString(haikeMerchantContractRequest));

            response = call(haikeContractUrl + "/front-api/merch/aggregation-apply", request, haikeParam);
            String resultCode = BeanUtil.getPropString(response, "result_code");
            String resultMsg = BeanUtil.getPropString(response, "result_msg");
            String merchNo = BeanUtil.getPropString(response, "merch_no");
            if ("10000".equals(resultCode)) {
                message = "进件成功";
                //关联收单机构终端ID
                String providerTerminalId = providerTerminalIdBiz.getHaiKeProviderTerminalIdForMerchant();
                merchantProviderParams = handleContractHaikeSuccess(merchantId, merchantSn, merchNo, haikeParam, new HashMap<>(), providerTerminalId);
            } else {
                code = errorInfoUtil.buildResultCode(ErrorInfoUtil.HAIKE_ERROR_INFO, resultCode);
                message = resultMsg;
            }
        } catch (ContractException e) {
            log.error("haike contractMerchant error", e);
            code = errorInfoUtil.buildResultCode(ErrorInfoUtil.HAIKE_ERROR_INFO, String.valueOf(e.getCode()));
            if (WosaiStringUtils.isEmpty(message)) {
                message = e.getMessage();
            }
        } catch (Exception e) {
            log.error("haike contractMerchant error", e);
            code = Constant.RESULT_CODE_SYSTEM_EXCEPTION;
            message = e.getMessage();
            if (e.getCause() != null) {
                message = e.getCause().getMessage();
            }
        }

        ContractResponse result = new ContractResponse();
        result.setCode(code);
        result.setMessage(message);
        result.setRequestParam(request);
        result.setResponseParam(response);
        result.setTradeParam(tradeParams);
        result.setMerchantProviderParamsId(WosaiMapUtils.getString(merchantProviderParams, DaoConstants.ID));
        return result;
    }

    /**
     * 进件成功处理
     *
     * @param merchantSn
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> handleContractHaikeSuccess(String merchantId, String merchantSn, String merchNo, HaikeParam haikeParam, Map<String, Object> tradeParams, String providerTerminalId) {
        Map<String, Object> merchantProviderParams = providerTradeParamsService.saveMerchantProviderParams(merchantSn, merchantSn, haikeParam.getChannel_no(), merchNo, merchNo, merchNo,
                ProviderEnum.PROVIDER_HAIKE.getValue(), PaywayEnum.ACQUIRER.getValue(), MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL, haikeParam, null, null, null, tradeParams);
        //关联收单机构终端ID
        providerTerminalBiz.merchantConnectionProviderTerminal(merchantSn, providerTerminalId, merchNo, ProviderEnum.PROVIDER_HAIKE.getValue());
        tradeConfigService.updateHKTradeParams(merchantId, CollectionUtil.hashMap("hk_mch_id", merchNo));

        return merchantProviderParams;
    }


    private void haveNet(Map<String, Object> tradeParams, Map params, Map context) throws ContractException {
        String payMerchantId = BeanUtil.getPropString(params, MerchantProviderParams.PAY_MERCHANT_ID);
        String providerMerchantId = BeanUtil.getPropString(params, MerchantProviderParams.PROVIDER_MERCHANT_ID);
        if ("1".equals(BeanUtil.getPropString(context, "type"))) {
            return;
        }
        if (!StringUtil.empty(providerMerchantId) && !StringUtil.empty(payMerchantId)) {
            tradeParams.put(MerchantProviderTradeParams.PROVIDER_MERCHANT_ID, providerMerchantId);
            throw new ContractException(Constant.RESULT_CODE_SUCCESSS, "商户已报备，无需重复报备");
        }
    }


    private Map<String, Object> call(String url, Map request, HaikeParam haikeParam) throws Exception {
        for (Object key : request.keySet().toArray()) {
            Object value = request.get(key);
            if (value == null || WosaiStringUtils.isEmpty(value.toString())) {
                request.remove(key);
            }
        }
        //图片压缩
        request.put("agent_no", haikeParam.getAgentNo());
        request.put("req_id", haikeBiz.nextReqId());
        String sign = HaikeSignature.getSign(haikeParam.getRsaKey(), JSONObject.parseObject(JSONObject.toJSONString(request)));
        request.put("sign", sign);
        log.info("call haike {} request: {}", url, JSON.toJSONString(request));
        HttpClientResultBO response = HttpClientUtils.doPost(url, null, JSONObject.toJSONString(request));
        log.info("call haike {} response {}", url, response.getContent());
        return JSON.parseObject(response.getContent());
    }

    @Override
    public ContractResponse updateMerchant(Map contextParam, HaikeParam haikeParam) {
        Map<String, Object> merchantProviderParams;
        //0.重复报备校验
        Map<String, Object> merchant = (Map) contextParam.get("merchant");
        String merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
        try {
            merchantProviderParams = providerTradeParamsService.getMerchantProviderParams(merchantSn, "haike", PaywayEnum.ACQUIRER.getValue(), String.valueOf(MerchantProviderParams.PROVIDER_HAIKE));
            if (merchantProviderParams == null) {
                throw new ContractBizException("商户还未入网");
            }
            HaikeMerchantContractRequest request = haikeBiz.buildContractRequest(contextParam, haikeParam);
            String merchNo = BeanUtil.getPropString(merchantProviderParams, MerchantProviderParams.PROVIDER_MERCHANT_ID);
            request.setMerch_no(merchNo);
            removeUnMcc(merchNo, haikeParam, request);
            Map requestParams = (Map<String, String>) JSONObject.parse(JSONObject.toJSONString(request));
            CustomFieldsUtil.fillCustomFields(requestParams, contextParam);
            Map response = call(haikeContractUrl + "/front-api/merch/aggregation-modify", requestParams, haikeParam);
            String resultCode = BeanUtil.getPropString(response, "result_code");
            String resultMsg = BeanUtil.getPropString(response, "result_msg");
            ContractResponse contractResponse = new ContractResponse()
                    .setRequestParam(requestParams)
                    .setResponseParam(response)
                    .setMessage(resultCode);
            if ("10000".equals(resultCode)) {
                contractResponse.setCode(200);
            } else {
                contractResponse.setCode(errorInfoUtil.buildResultCode(ErrorInfoUtil.HAIKE_ERROR_INFO, resultCode));
            }
            contractResponse.setMessage(resultMsg);
            return contractResponse;
        } catch (ContractException e) {
            log.error("haike updateMerchant error", e);
            return new ContractResponse().setCode(e.getCode()).setMessage(e.getMessage());
        } catch (Exception e) {
            log.error("updateMerchant to haike failed payMerchantId {}", merchantSn, e);
            chatBotUtil.sendMessageToContractWarnChatBot(String.format("海科 商户信息修改出现异常:%s,%s", merchantSn, e.toString()));
            return new ContractResponse().setCode(Constant.RESULT_CODE_SYSTEM_EXCEPTION).setMessage(e.getMessage());
        }
    }

    /**
     * 云闪付进件成功的情况下不更新un_mcc
     *
     * @param providerMerchantId
     * @param haikeParam
     * @param request
     */
    private void removeUnMcc(String providerMerchantId, HaikeParam haikeParam, HaikeMerchantContractRequest request) {
        try {
            ContractResponse response = queryMerchantContractResult(providerMerchantId, haikeParam);
            if (response.isSuccess()) {
                request.getMerchant_data().setUn_mcc(null);
            }
        } catch (Exception e) {
            log.warn("removeUnMcc error {}", providerMerchantId);
        }
    }

    @Override
    public ContractResponse updateBasicMerchant(Map contextParam, HaikeParam haikeParam) {
        Map<String, Object> merchantProviderParams;
        //0.重复报备校验
        Map<String, Object> merchant = (Map) contextParam.get("merchant");
        String merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
        try {
            merchantProviderParams = providerTradeParamsService.getMerchantProviderParams(merchantSn, "haike", PaywayEnum.ACQUIRER.getValue(), String.valueOf(MerchantProviderParams.PROVIDER_HAIKE));
            if (merchantProviderParams == null) {
                throw new ContractBizException("商户还未入网");
            }
            HaikeMerchantContractRequest request = haikeBiz.buildContractRequest(contextParam, haikeParam);
            String merchNo = BeanUtil.getPropString(merchantProviderParams, MerchantProviderParams.PROVIDER_MERCHANT_ID);
            request.setMerch_no(merchNo);
            removeUnMcc(merchNo, haikeParam, request);
            Map requestParams = (Map<String, String>) JSONObject.parse(JSONObject.toJSONString(request));
            CustomFieldsUtil.fillCustomFields(requestParams, contextParam);
            Map response = call(haikeContractUrl + "/front-api/merch/aggregation-modify", requestParams, haikeParam);
            String resultCode = BeanUtil.getPropString(response, "result_code");
            String resultMsg = BeanUtil.getPropString(response, "result_msg");
            ContractResponse contractResponse = new ContractResponse()
                    .setRequestParam(requestParams)
                    .setResponseParam(response)
                    .setMessage(resultCode);
            if ("10000".equals(resultCode)) {
                contractResponse.setCode(200);
            } else {
                contractResponse.setCode(errorInfoUtil.buildResultCode(ErrorInfoUtil.HAIKE_ERROR_INFO, resultCode));
            }
            contractResponse.setMessage(resultMsg);
            return contractResponse;
        } catch (ContractException e) {
            log.error("haike updateMerchant error", e);
            return new ContractResponse().setCode(e.getCode()).setMessage(e.getMessage());
        } catch (Exception e) {
            log.error("updateMerchant to haike failed payMerchantId {}", merchantSn, e);
            chatBotUtil.sendMessageToContractWarnChatBot(String.format("海科 商户信息修改出现异常:%s,%s", merchantSn, e.toString()));
            return new ContractResponse().setCode(Constant.RESULT_CODE_SYSTEM_EXCEPTION).setMessage(e.getMessage());
        }
    }

    /**
     * 修改商户结算账户
     * copy from com.wosai.upay.merchant.contract.service.HaikeServiceImpl#updateBankAccountMerchant(java.util.Map, com.wosai.upay.merchant.contract.model.provider.HaikeParam)
     *
     * @param acquirerMerchantId 收单机构商户号
     * @param contextParam       参数上下文
     * @param haikeParam         haike交易参数
     * @return 结果
     */
    @Override
    public ContractResponse updateBankAccountMerchant(String acquirerMerchantId, Map contextParam, HaikeParam haikeParam) {
        Map<String, Object> merchant = (Map) contextParam.get("merchant");
        String merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
        try {
            HaikeMerchantContractRequest request = haikeBiz.buildContractRequest(contextParam, haikeParam);
            request.setMerch_no(acquirerMerchantId);
            request.getMerchant_data().setUn_mcc(acquirerMerchantId.substring(8, 12));
            Map requestParams = (Map<String, String>) JSONObject.parse(JSONObject.toJSONString(request));
            CustomFieldsUtil.fillCustomFields(requestParams, contextParam);
            Map response = call(haikeContractUrl + "/front-api/merch/aggregation-modify", requestParams, haikeParam);
            String resultCode = BeanUtil.getPropString(response, "result_code");
            String resultMsg = BeanUtil.getPropString(response, "result_msg");
            ContractResponse contractResponse = new ContractResponse()
                    .setRequestParam(requestParams)
                    .setResponseParam(response)
                    .setMessage(resultCode);
            if ("10000".equals(resultCode)) {
                contractResponse.setCode(200);
            } else {
                contractResponse.setCode(errorInfoUtil.buildResultCode(ErrorInfoUtil.HAIKE_ERROR_INFO, resultCode));
            }
            contractResponse.setMessage(resultMsg);
            return contractResponse;
        } catch (ContractException e) {
            log.error("haike updateMerchant error", e);
            return new ContractResponse().setCode(e.getCode()).setMessage(e.getMessage());
        } catch (Exception e) {
            log.error("updateMerchant to haike failed payMerchantId {}", merchantSn, e);
            return new ContractResponse().setCode(Constant.RESULT_CODE_SYSTEM_EXCEPTION).setMessage(e.getMessage());
        }
    }

    @Override
    public ContractResponse updateBankAccountMerchant(Map contextParam, HaikeParam haikeParam) {
        Map<String, Object> merchantProviderParams;
        //0.重复报备校验
        Map<String, Object> merchant = (Map) contextParam.get("merchant");
        String merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
        try {
            merchantProviderParams = providerTradeParamsService.getMerchantProviderParams(merchantSn, "haike", PaywayEnum.ACQUIRER.getValue(), String.valueOf(MerchantProviderParams.PROVIDER_HAIKE));
            if (merchantProviderParams == null) {
                throw new ContractBizException("商户还未入网");
            }
            HaikeMerchantContractRequest request = haikeBiz.buildContractRequest(contextParam, haikeParam);
            String merchNo = BeanUtil.getPropString(merchantProviderParams, MerchantProviderParams.PROVIDER_MERCHANT_ID);
            request.setMerch_no(merchNo);
            request.getMerchant_data().setUn_mcc(merchNo.substring(8, 12));
            Map requestParams = (Map<String, String>) JSONObject.parse(JSONObject.toJSONString(request));
            CustomFieldsUtil.fillCustomFields(requestParams, contextParam);
            Map response = call(haikeContractUrl + "/front-api/merch/aggregation-modify", requestParams, haikeParam);
            String resultCode = BeanUtil.getPropString(response, "result_code");
            String resultMsg = BeanUtil.getPropString(response, "result_msg");
            ContractResponse contractResponse = new ContractResponse()
                    .setRequestParam(requestParams)
                    .setResponseParam(response)
                    .setMessage(resultCode);
            if ("10000".equals(resultCode)) {
                contractResponse.setCode(200);
            } else {
                contractResponse.setCode(errorInfoUtil.buildResultCode(ErrorInfoUtil.HAIKE_ERROR_INFO, resultCode));
            }
            contractResponse.setMessage(resultMsg);
            return contractResponse;
        } catch (ContractException e) {
            log.error("haike updateMerchant error", e);
            return new ContractResponse().setCode(e.getCode()).setMessage(e.getMessage());
        } catch (Exception e) {
            log.error("updateMerchant to haike failed payMerchantId {}", merchantSn, e);
            chatBotUtil.sendMessageToContractWarnChatBot(String.format("海科 商户信息修改出现异常:%s,%s", merchantSn, e.toString()));
            return new ContractResponse().setCode(Constant.RESULT_CODE_SYSTEM_EXCEPTION).setMessage(e.getMessage());
        }
    }

    @Override
    public ContractResponse queryMerchant(Map contextParam, HaikeParam haikeParam) {
        Map<String, Object> merchantProviderParams;
        Map<String, String> request = new HashMap<>();
        //0.重复报备校验
        Map<String, Object> merchant = (Map) contextParam.get("merchant");
        String merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
        try {
            merchantProviderParams = providerTradeParamsService.getMerchantProviderParams(merchantSn, "haike", PaywayEnum.ACQUIRER.getValue(), String.valueOf(MerchantProviderParams.PROVIDER_HAIKE));
            if (merchantProviderParams == null) {
                throw new ContractBizException("商户还未入网");
            }
            request.put("agent_apply_no", BeanUtil.getPropString(merchantProviderParams, "out_merchant_sn"));
            request.put("merch_no", BeanUtil.getPropString(merchantProviderParams, "pay_merchant_id"));
            Map response = call(haikeContractUrl + "/front-api/merch/aggregation-info", (Map<String, String>) JSONObject.parse(JSONObject.toJSONString(request)), haikeParam);
            String resultCode = BeanUtil.getPropString(response, "result_code");
            String resultMsg = BeanUtil.getPropString(response, "result_msg", BeanUtil.getPropString(response, "return_msg"));
            ContractResponse contractResponse = new ContractResponse()
                    .setRequestParam((Map<String, Object>) JSONObject.parse(JSONObject.toJSONString(request)))
                    .setResponseParam(response)
                    .setMessage(resultMsg);
            if ("10000".equals(resultCode)) {
                contractResponse.setCode(200);
            } else {
                contractResponse.setCode(errorInfoUtil.buildResultCode(ErrorInfoUtil.HAIKE_ERROR_INFO, resultCode));
            }
            return contractResponse;
        } catch (Exception e) {
            log.error("queryMerchant to haike failed payMerchantId {}", merchantSn, e);
            chatBotUtil.sendMessageToContractWarnChatBot(String.format("haike 商户进件出现异常:%s,%s", merchantSn, e.toString()));
            return new ContractResponse().setCode(Constant.RESULT_CODE_SYSTEM_EXCEPTION).setMessage(e.getMessage());
        }
    }

    @Override
    public ContractResponse queryMerchantContractResult(String providerMerchantId, HaikeParam haikeParam) {
        Map<String, Object> merchantProviderParams;
        Map<String, String> request = new HashMap<>();
        //0.重复报备校验
        try {
            merchantProviderParams = providerTradeParamsService.getProviderParamsByProviderMerchantIdAndPayway(providerMerchantId, PaywayEnum.ACQUIRER.getValue());
            if (merchantProviderParams == null) {
                throw new ContractBizException("商户还未入网");
            }
            request.put("agent_apply_no", BeanUtil.getPropString(merchantProviderParams, "out_merchant_sn"));
            request.put("merch_no", BeanUtil.getPropString(merchantProviderParams, "pay_merchant_id"));
            Map response = call(haikeContractUrl + "/front-api/merch/aggregation-info", (Map<String, String>) JSONObject.parse(JSONObject.toJSONString(request)), haikeParam);
            String resultCode = BeanUtil.getPropString(response, "result_code");
            String resultMsg = BeanUtil.getPropString(response, "result_msg");
            ContractResponse contractResponse = new ContractResponse()
                    .setRequestParam((Map<String, Object>) JSONObject.parse(JSONObject.toJSONString(request)))
                    .setResponseParam(response)
                    .setMessage(resultMsg);
            if ("10000".equals(resultCode)) {
                contractResponse.setCode(200);
            } else {
                contractResponse.setCode(errorInfoUtil.buildResultCode(ErrorInfoUtil.HAIKE_ERROR_INFO, resultCode));
            }

            if (contractResponse.isSuccess()) {
                Map responseParam = contractResponse.getResponseParam();
                Map channelBiz = WosaiMapUtils.getMap(responseParam, "channel_biz_info");
                if (WosaiMapUtils.isEmpty(channelBiz)) {
                    return new ContractResponse().setCode(Constant.RESULT_CODE_BIZ_EXCEPTION).setMessage("未查询到银联商户报备信息");
                }
                Map bankInfo = WosaiMapUtils.getMap(channelBiz, "bank_info");
                String applyStatus = BeanUtil.getPropString(bankInfo, "apply_status");
                String reportStatus = BeanUtil.getPropString(bankInfo, "report_status");
                if (StringUtil.empty(applyStatus) || StringUtil.empty(reportStatus) || "0".equals(applyStatus) || "0".equals(reportStatus)) {
                    return new ContractResponse().setCode(Constant.RESULT_CODE_SYSTEM_EXCEPTION).setMessage("报备中，重试查询");
                }
                if ("3".equals(BeanUtil.getPropString(bankInfo, "apply_status")) && "3".equals(BeanUtil.getPropString(bankInfo, "report_status"))) {
                    Map unionPayParams = saveUnionPayParams(responseParam, merchantProviderParams, haikeParam);
                    contractResponse.setMessage("报备成功");
                    if (WosaiMapUtils.isNotEmpty(unionPayParams)) {
                        contractResponse.setMerchantProviderParamsId(BeanUtil.getPropString(unionPayParams, DaoConstants.ID));
                    }
                    return contractResponse;
                } else {
                    return new ContractResponse().setCode(Constant.RESULT_CODE_BIZ_EXCEPTION).setMessage(BeanUtil.getPropString(bankInfo, "report_msg") + BeanUtil.getPropString(bankInfo, "apply_msg"));
                }
            } else {
                return new ContractResponse().setCode(contractResponse.getCode()).setMessage(contractResponse.getMessage());
            }
        } catch (Exception e) {
            log.error("queryMerchantContractResult to haike failed providerMerchantId {}", providerMerchantId, e);
            chatBotUtil.sendMessageToContractWarnChatBot(String.format("haike 商户查询进件状态出现异常:%s,%s", providerMerchantId, e.toString()));
            return new ContractResponse().setCode(Constant.RESULT_CODE_SYSTEM_EXCEPTION).setMessage(e.getMessage());
        }
    }

    private Map saveUnionPayParams(Map responseParam, Map acquirerParams, HaikeParam haikeParam) {
        // 检查是否有云闪付参数
        String merchantSn = BeanUtil.getPropString(acquirerParams, MerchantProviderParams.MERCHANT_SN);
        Map unionPayParams = providerTradeParamsService.getProviderParamsByProvider(merchantSn, MerchantProviderParams.PROVIDER_HAIKE, PaywayEnum.UNIONPAY.getValue());
        if (WosaiMapUtils.isNotEmpty(unionPayParams)) {
            return unionPayParams;
        }

        String bankMerchNo = BeanUtil.getPropString(responseParam, "bank_biz_info.bank_merch_no");
        if (WosaiStringUtils.isEmpty(bankMerchNo)) {
            return null;
        }
        String merchNo = BeanUtil.getPropString(acquirerParams, MerchantProviderParams.PROVIDER_MERCHANT_ID);

        // 云闪付保存的是经营名称
        String merchantName = BeanUtil.getPropString(responseParam, "merch_short_name");
        Map<String, Object> extra = new HashMap();
        Map tradeParams = CollectionUtil.hashMap(
                "bankMerchNo", bankMerchNo);
        extra.put("tradeParams", tradeParams);
        return providerTradeParamsService.saveMerchantProviderParams(merchantSn, merchantSn, UNION_CHANNEL_NO, merchNo, merchNo, bankMerchNo,
                ProviderEnum.PROVIDER_HAIKE.getValue(), PaywayEnum.UNIONPAY.getValue(), MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL, haikeParam, null, null, merchantName, extra);

    }

    @Override
    public ContractResponse contractWeixinWithParams(Map contextParam, UnionWeixinParam wechatParam) {

        Map<String, Object> tradeParams = Maps.newHashMap();
        Map<String, Object> merchantProviderParams = Maps.newHashMap();

        int code = Constant.RESULT_CODE_SUCCESSS;
        String message;
        String merchantSn = null;
        ContractResponse contractResponse = new ContractResponse();

        Map unionResponse = Maps.newHashMap();
        Map<String, Object> unionRequest = Maps.newHashMap();
        try {
            String contractType = BeanUtil.getPropString(contextParam, "type");
            Map<String, Object> merchant = (Map) contextParam.get("merchant");
            merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
            Map<String, Object> acquirerParam = providerTradeParamsService.getMerchantProviderParams(merchantSn, "haike", PaywayEnum.ACQUIRER.getValue(), String.valueOf(MerchantProviderParams.PROVIDER_HAIKE));
            if (acquirerParam == null) {
                throw new ContractBizException("商户还未入网");
            }
            Map params = providerTradeParamsService.getMerchantProviderParams(merchantSn, wechatParam.getChannel_no(), MerchantProviderParams.PAYWAY_WEIXIN, wechatParam.getProvider());
            String payMerchantId = BeanUtil.getPropString(params, MerchantProviderParams.PAY_MERCHANT_ID);
            String providerMerchantId = BeanUtil.getPropString(params, MerchantProviderParams.PROVIDER_MERCHANT_ID);
            //0.重复报备校验
            if (!StringUtils.empty(providerMerchantId) && !StringUtils.empty(payMerchantId) && StringUtils.isEmpty(contractType)) {
                tradeParams.put(MerchantProviderTradeParams.PROVIDER_MERCHANT_ID, providerMerchantId);
                tradeParams.put(MerchantProviderTradeParams.ALIPAY_MERCHANT_ID, payMerchantId);
                merchantProviderParams = params;
                throw new ContractException(Constant.RESULT_CODE_SUCCESSS, "商户已报备，无需重复报备");
            }
            //1,组装参数
            unionRequest = unionBiz.contractWechat(contextParam, wechatParam, false);

            //2.调用请求
            unionResponse = newUnionBiz.callWeixin("/submch/manage/add", wechatParam, unionRequest);
            //3.处理响应
            unionBiz.package259Response(merchantSn, contractResponse, unionResponse, "商户报备银联微信");
            if (contractResponse.isSuccess()) {
                Map providerParamsNoPayWay = providerTradeParamsService.getProviderParamsByProvider(merchantSn, ProviderEnum.PROVIDER_HAIKE.getValue(), PaywayEnum.ACQUIRER.getValue());
                providerMerchantId = BeanUtil.getPropString(providerParamsNoPayWay, MerchantProviderParams.PROVIDER_MERCHANT_ID);
                payMerchantId = BeanUtil.getPropString(unionResponse, "sub_mch_id");
                if (!com.wosai.data.util.StringUtil.empty(payMerchantId)) {
                    //保存参数
                    merchantProviderParams = providerTradeParamsService.saveMerchantProviderParams(merchantSn, merchantSn, wechatParam.getChannel_no(), wechatParam.getChannel_no(), providerMerchantId, payMerchantId,
                            Integer.parseInt(wechatParam.getProvider()), wechatParam.getPayway(),
                            MerchantProviderParams.PARAMS_CONFIG_STATUS_PRE, wechatParam,
                            BeanUtil.getPropString(unionRequest, "business"),
                            BeanUtil.getPropInt(contextParam, "wxUseType", 1),
                            BeanUtil.getPropString(unionRequest, "merchant_name"),
                            commonBiz.buildCustomFieldsExtra(contextParam));
                    //新增子商户号绑定终端任务
                    terminalRepository.addBoundTerminalTask(merchantSn, payMerchantId, Integer.parseInt(wechatParam.getProvider()), MerchantProviderParams.PAYWAY_WEIXIN);
                    tradeParams.put(MerchantProviderTradeParams.PROVIDER_MERCHANT_ID, providerMerchantId);
                    tradeParams.put(MerchantProviderTradeParams.WEIXIN_MERCHANT_ID, payMerchantId);
                }
                message = "报备微信成功";
                try {
                    syncMerchantToHaike(payMerchantId);
                } catch (Exception e) {
                    log.error("haike 微信终端同步异常  payMerchantId {}", merchantSn, e);
                }
            } else {
                //报备失败 业务异常 或 系统异常
                throw new ContractException(contractResponse.getCode(), contractResponse.getMessage());
            }
        } catch (ContractException e) {
            log.error("haike 进件微信异常  payMerchantId {}", merchantSn, e);
            code = e.getCode();
            message = e.getMessage();
        } catch (Exception e) {
            code = Constant.RESULT_CODE_SYSTEM_EXCEPTION;
            message = e.getMessage();
            log.error("haike 进件微信系统异常  payMerchantId {}", merchantSn, e);
            chatBotUtil.sendMessageToContractWarnChatBot(String.format("haike 商户报备微信出现异常:%s,%s", merchantSn, e.toString()));
        }

        contractResponse.setRequestParam(unionRequest);
        contractResponse.setResponseParam(unionResponse);
        contractResponse.setTradeParam(tradeParams);
        contractResponse.setMerchantProviderParamsId(WosaiMapUtils.getString(merchantProviderParams, DaoConstants.ID));
        contractResponse.setMessage(message);
        contractResponse.setCode(code);

        return contractResponse;
    }

    @Override
    public ContractResponse updateWeixinWithParams(Map contextParam, UnionWeixinParam wechatParam) {
        return updateWechatBySubMchId(null, contextParam, wechatParam);
    }

    @Override
    public ContractResponse updateWechatBySubMchId(String subMchId, Map contextParam, UnionWeixinParam wechatParam) {
        Map<String, Object> unionRequest = Maps.newHashMap();
        Map<String, Object> unionResponse = Maps.newHashMap();
        Map<String, Object> tradeParams = Maps.newHashMap();
        ContractResponse contractResponse = new ContractResponse();
        String merchantSn = null;
        int code = 200;
        String message;
        try {
            Map<String, Object> merchant = (Map) contextParam.get("merchant");
            merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);

            Map params = WosaiStringUtils.isEmpty(subMchId) ?
                    providerTradeParamsService.getUsedMerchantProviderParams(merchantSn, wechatParam.getChannel_no(), MerchantProviderParams.PAYWAY_WEIXIN, wechatParam.getProvider()) :
                    providerTradeParamsService.getMerchantProviderParamsByPayMerchantId(subMchId);

            String wechatMerchantId = BeanUtil.getPropString(params, MerchantProviderParams.PAY_MERCHANT_ID);
            String providerMerchantId = BeanUtil.getPropString(params, MerchantProviderParams.PROVIDER_MERCHANT_ID);
            if (MapUtils.isEmpty(params)) {
                throw new ContractException(Constant.RESULT_CODE_SUCCESSS, "微信商户不存在"); //当作成功处理
            }

            //1,参数组装
            unionRequest = unionBiz.updateWechat(wechatMerchantId, params, contextParam, wechatParam, false);
            try {
                if (WosaiStringUtils.isNotEmpty((String) unionRequest.get("merchant_shortname")) ||
                        WosaiStringUtils.isNotEmpty((String) unionRequest.get("service_phone"))) {
                    MchInfo mchInfo = queryWeChatMchInfoParams(wechatMerchantId);
                    unionBiz.removeNotNeedUpdateParams(unionRequest, mchInfo);
                }
            } catch (Exception e) {
                log.error("去除更新字段异常  wechatMerchantId {}", wechatMerchantId, e);
            }

            //2,调用请求
            unionResponse = newUnionBiz.callWeixin("/submch/manage/upd", wechatParam, unionRequest);

            //3.处理响应
            unionBiz.package259Response(merchantSn, contractResponse, unionResponse, "商户更新银联微信");

            if (contractResponse.isSuccess()) {
                tradeParams.put(MerchantProviderTradeParams.PROVIDER_MERCHANT_ID, providerMerchantId);
                tradeParams.put(MerchantProviderTradeParams.WEIXIN_MERCHANT_ID, wechatMerchantId);
                message = "微信修改成功";
                String merchantShortname = (String) unionRequest.get("merchant_shortname");
                if (apolloParamsConfig.getBoolean("haike-check-wx-shortname", true) && WosaiStringUtils.isNotEmpty(merchantShortname)) {
                    threadPool.submit(() -> {
                        MchInfo mchInfo = queryWeChatMchInfoParams(wechatMerchantId);
                        log.info("更新微信简称结果 {} {} {}", mchInfo.getMch_id(), merchantShortname, mchInfo.getMerchant_shortname());
                    });
                }

            } else {
                //更新微信失败 业务异常 或 系统异常
                throw new ContractException(contractResponse.getCode(), contractResponse.getMessage());
            }
        } catch (ContractException e) {
            log.error("银联 更新微信异常  payMerchantId {}", merchantSn, e);
            code = e.getCode();
            message = e.getMessage();
        } catch (Exception e) {
            code = Constant.RESULT_CODE_SYSTEM_EXCEPTION;
            message = e.getMessage();
            log.error("银联 更新微信系统异常  payMerchantId {}", merchantSn, e);
            chatBotUtil.sendMessageToContractWarnChatBot(String.format("银联 商户更新微信出现异常:%s,%s", merchantSn, e.toString()));
        }

        contractResponse.setCode(code);
        contractResponse.setMessage(message);
        contractResponse.setRequestParam(unionRequest);
        contractResponse.setResponseParam(unionResponse);
        contractResponse.setTradeParam(tradeParams);
        return contractResponse;
    }


    @Override
    public MchInfo queryWeChatMchInfoParams(String subMchId) {
        try {
            UnionWeixinParam wechatParam = contractBaseBiz.buildContractParamsByPayMchId(subMchId, UnionWeixinParam.class);
            //1,组装参数
            Map<String, Object> unionRequest = unionBiz.queryWechat(subMchId, wechatParam);

            //2.调用请求
            Map<String, Object> unionResponse = newUnionBiz.callWeixin("/submch/qry", wechatParam, unionRequest);
            //3.处理响应
            ContractResponse contractResponse = new ContractResponse();
            unionBiz.package259Response(subMchId, contractResponse, unionResponse, "微信子商户号查询");
            if (contractResponse.isSuccess()) {
                String mchInfo = BeanUtil.getPropString(unionResponse, "mchinfo");
                return JSONObject.parseArray(mchInfo, MchInfo.class).get(0);
            } else {
                throw new ContractBizException(contractResponse.getMessage());
            }
        } catch (Exception e) {
            throw new ContractSysException("查询信息异常: " + e.getMessage());
        }
    }


    /**
     * @param subMchId
     * @return
     * @throws MpayException
     * @throws MpayApiNetworkError
     */
    @Override
    public Map queryAlySubMch(String subMchId) {
        UnionAlipayParam alipayParam = contractBaseBiz.buildContractParamsByPayMchId(subMchId, UnionAlipayParam.class);

        JSONObject bizContent = new JSONObject();
        bizContent.put("sub_merchant_id", subMchId);
        Map<String, String> params = CollectionUtil.hashMap(
                "app_id", alipayParam.getUnion_app_id(),
                "method", "ant.merchant.expand.indirect.query",
                "format", "json",
                "charset", "utf-8",
                "timestamp", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                "version", "1.0",
                "biz_content", bizContent.toJSONString()
        );

        try {
            Map<String, Object> response = newUnionBiz.callAlipay(alipayParam, params);
            String subMsg = BeanUtil.getPropString(response, UnionBusinessFileds.SUB_MSG);
            logger.info("支付宝查询商户：{},{}", subMchId, response);
            return response;
        } catch (Exception e) {
            log.error("支付宝子商户号查询支付宝子商户信息错误 {}", subMchId, e);
            return null;
        }
    }

    @Override
    public ContractResponse contractAlipayWithParams(Map contextParam, UnionAlipayParam alipayParam) {
        Map<String, String> request = newUnionBiz.buildAliCommonParam("ant.merchant.expand.indirect.create", alipayParam);
        Map<String, Object> response = Maps.newHashMap();
        Map<String, Object> tradeParams = Maps.newHashMap();
        Map merchantProviderParams = Maps.newHashMap();
        int code = 200;
        String message = "";
        String merchantSn;
        try {
            Map<String, Object> merchant = (Map) contextParam.get("merchant");
            merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
            String contractType = BeanUtil.getPropString(contextParam, "type");
            Map<String, Object> acquirerParam = providerTradeParamsService.getMerchantProviderParams(merchantSn, "haike", PaywayEnum.ACQUIRER.getValue(), String.valueOf(MerchantProviderParams.PROVIDER_HAIKE));
            if (acquirerParam == null) {
                throw new ContractBizException("商户还未入网");
            }
            final Map params = providerTradeParamsService.getMerchantProviderParams(merchantSn, alipayParam.getChannel_no(), MerchantProviderParams.PAYWAY_ALIPAY2, alipayParam.getProvider());
            String aliMerchantId = BeanUtil.getPropString(params, MerchantProviderParams.PAY_MERCHANT_ID);
            String providerMerchantId = BeanUtil.getPropString(params, MerchantProviderParams.PROVIDER_MERCHANT_ID);
            //0.重复报备校验
            if (!StringUtils.empty(providerMerchantId) && !StringUtils.empty(aliMerchantId) && StringUtils.isEmpty(contractType)) {
                tradeParams.put(MerchantProviderTradeParams.PROVIDER_MERCHANT_ID, providerMerchantId);
                tradeParams.put(MerchantProviderTradeParams.ALIPAY_MERCHANT_ID, aliMerchantId);
                merchantProviderParams = params;
                throw new ContractException(Constant.RESULT_CODE_SUCCESSS, "商户已报备，无需重复报备");
            }
            //1构建参数
            final Map<String, Object> unionBizContractAliParam = newUnionBiz.getContractAliParam(contextParam, alipayParam, false);
            //设置自定义参数
            newUnionBiz.setCustomFields(contextParam, unionBizContractAliParam);
            String externalId = BeanUtil.getPropString(unionBizContractAliParam, "external_id");
            request.put("biz_content", JSONObject.toJSONString(unionBizContractAliParam));
            //2发起请求
            response = newUnionBiz.callAlipay(alipayParam, request);
            //3处理响应
            boolean success = Objects.equals(UnionConstant.RETURN_ALIPAY_CODE_SUCCESS, BeanUtil.getPropString(response, UnionBusinessFileds.CODE))
                    && Objects.equals("Success", BeanUtil.getPropString(response, UnionBusinessFileds.MSG));
            if (success) {
                aliMerchantId = BeanUtil.getPropString(response, LakalaWanmaBusinessFileds.SUB_MERCHANT_ID);
                //银联商户号
                String aliMcc = BeanUtil.getPropString(unionBizContractAliParam, "mcc");
                String merchantName = BeanUtil.getPropString(unionBizContractAliParam, "name");
                Map providerParamsNoPayWay = providerTradeParamsService.getProviderParamsByProvider(merchantSn, ProviderEnum.PROVIDER_HAIKE.getValue(), PaywayEnum.ACQUIRER.getValue());
                providerMerchantId = BeanUtil.getPropString(providerParamsNoPayWay, MerchantProviderParams.PROVIDER_MERCHANT_ID);
                if (!StringUtils.empty(aliMerchantId)) {
                    merchantProviderParams = providerTradeParamsService.saveAlipayMerchantProviderParams(merchantSn, externalId, alipayParam.getChannel_no(), alipayParam.getOrg_pid(), providerMerchantId,
                            aliMerchantId, Integer.valueOf(alipayParam.getProvider()),
                            alipayParam.getPayway(), MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL,
                            alipayParam, aliMcc, merchantName, commonBiz.buildCustomFieldsExtra(contextParam));
                    terminalRepository.addBoundTerminalTask(merchantSn, aliMerchantId, Integer.valueOf(alipayParam.getProvider()), MerchantProviderParams.PAYWAY_ALIPAY2);
                }
                tradeParams.put(MerchantProviderTradeParams.PROVIDER_MERCHANT_ID, providerMerchantId);
                tradeParams.put(MerchantProviderTradeParams.ALIPAY_MERCHANT_ID, aliMerchantId);
                message = "进件支付宝成功";
                try {
                    syncMerchantToHaike(aliMerchantId);
                } catch (Exception e) {
                    log.error("haike 支付宝终端同步异常  payMerchantId {}", merchantSn, e);
                }
            } else {
                String unionSubCode = BeanUtil.getPropString(response, UnionBusinessFileds.SUB_CODE);
                message = BeanUtil.getPropString(response, UnionBusinessFileds.SUB_MSG);
                throw new ContractException(errorInfoUtil.buildResultCode(ErrorInfoUtil.UNIONALI_ERROR_MESSAGE, unionSubCode), message);
            }
        } catch (ContractException e) {
            logger.error("haike contractAli exception :{}", e);
            code = e.getCode();
            if (WosaiStringUtils.isEmpty(message)) {
                message = e.getMessage();
            }
        } catch (Exception e) {
            logger.error("haike contractAli exception :{}", e);
            code = Constant.RESULT_CODE_SYSTEM_EXCEPTION;
            message = e.getMessage();
            if (e.getCause() != null) {
                message = e.getCause().getMessage();
            }
        }
        //组装返回结果
        Map<String, Object> map = Maps.newHashMap();
        map.putAll(request);
        return newUnionBiz.generateResult(tradeParams, code, message, map, response, merchantProviderParams);
    }

    @Override
    public ContractResponse updateAlipayWithParams(Map contextParam, UnionAlipayParam alipayParam) {
        return updateAlipayBySubMchId(null, contextParam, alipayParam);
    }

    @Override
    public ContractResponse updateAlipayBySubMchId(String subMchId, Map contextParam, UnionAlipayParam alipayParam) {
        Map<String, String> request = newUnionBiz.buildAliCommonParam("ant.merchant.expand.indirect.modify", alipayParam);
        Map<String, Object> response = Maps.newHashMap();
        Map<String, Object> tradeParams = Maps.newHashMap();
        Map<String, Object> providerParams = Maps.newHashMap();
        String merchantSn;
        int code = 200;
        String message = "";
        try {
            Map<String, Object> merchant = (Map) contextParam.get("merchant");
            merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
            providerParams = WosaiStringUtils.isEmpty(subMchId) ?
                    providerTradeParamsService.getMerchantProviderParams(merchantSn, alipayParam.getChannel_no(), MerchantProviderParams.PAYWAY_ALIPAY2, alipayParam.getProvider()) :
                    providerTradeParamsService.getMerchantProviderParamsByPayMerchantId(subMchId);
            subMchId = BeanUtil.getPropString(providerParams, MerchantProviderParams.PAY_MERCHANT_ID);
            //0.校验
            if (Objects.isNull(providerParams) || StringUtils.isEmpty(BeanUtil.getPropString(providerParams, MerchantProviderParams.PAY_MERCHANT_ID))) {
                throw new ContractException(Constant.RESULT_CODE_SUCCESSS, "商户未报备支付宝 无需更新信息");
            }
            //1组装参数
            final Map<String, Object> contractAliParam = newUnionBiz.getContractAliParam(contextParam, alipayParam, false);
            String merchantName = BeanUtil.getPropString(contractAliParam, "name");
            contractAliParam.putAll(CollectionUtil.hashMap(
                    "sub_merchant_id", BeanUtil.getPropString(providerParams, MerchantProviderParams.PAY_MERCHANT_ID),
                    "external_id", null,
                    "merchantState", null,
                    "pay_ctrl", null,
                    "name", merchantName
            ));
            if (!StringUtils.isEmpty(BeanUtil.getPropString(providerParams, MerchantProviderParams.ALI_MCC))) {
                contractAliParam.put("mcc", BeanUtil.getPropString(providerParams, MerchantProviderParams.ALI_MCC));
            }
            //设置自定义参数
            newUnionBiz.setCustomFields(contextParam, contractAliParam, providerParams);
            request.put("biz_content", JSONObject.toJSONString(contractAliParam));
            //2发起请求
            log.info("call newUnionUpdat request {}", JSON.toJSONString(request));
            response = newUnionBiz.callAlipay(alipayParam, request);
            log.info("call newUnionUpdat response {}", JSON.toJSONString(response));
            //3处理响应
            final String unionCode = BeanUtil.getPropString(response, UnionBusinessFileds.CODE);
            if (UnionConstant.RETURN_ALIPAY_CODE_SUCCESS.equalsIgnoreCase(unionCode)
                    && "Success".equalsIgnoreCase(BeanUtil.getPropString(response, UnionBusinessFileds.MSG))) {
                tradeParams.put(MerchantProviderTradeParams.PROVIDER_MERCHANT_ID, BeanUtil.getPropString(providerParams, MerchantProviderTradeParams.PROVIDER_MERCHANT_ID));
                tradeParams.put(MerchantProviderTradeParams.ALIPAY_MERCHANT_ID, BeanUtil.getPropString(providerParams, MerchantProviderParams.PAY_MERCHANT_ID));
                String aliMcc = WosaiMapUtils.getString(contractAliParam, BusinessFields.ALIPAY_MCC);
                Map updateMerchantProviderParams = CollectionUtil.hashMap(
                        MerchantProviderParams.MERCHANT_NAME, merchantName,
                        MerchantProviderParams.ALI_MCC, aliMcc
                );
                boolean updateNameResult = providerTradeParamsService.updateMerchantProviderParamBySubMchId(subMchId, updateMerchantProviderParams);
                if (!updateNameResult) {
                    log.error("tl update contractAlipayWithParams success,but update db fail");
                }
                message = "支付宝修改成功";
            } else {
                String unionSubCode = BeanUtil.getPropString(response, UnionBusinessFileds.SUB_CODE);
                message = BeanUtil.getPropString(response, UnionBusinessFileds.SUB_MSG);
                throw new ContractException(errorInfoUtil.buildResultCode(ErrorInfoUtil.UNIONALI_ERROR_MESSAGE, unionSubCode), message);
            }
        } catch (ContractException e) {
            logger.error("haike updateAli exception ", e);
            code = e.getCode();
            if (WosaiStringUtils.isEmpty(message)) {
                message = e.getMessage();
            }
        } catch (Exception e) {
            logger.error("haike updateAli exception ", e);
            code = Constant.RESULT_CODE_SYSTEM_EXCEPTION;
            message = e.getMessage();
            if (e.getCause() != null) {
                message = e.getCause().getMessage();
            }
        }
        //组装返回结果
        Map<String, Object> map = Maps.newHashMap();
        map.putAll(request);
        return newUnionBiz.generateResult(tradeParams, code, message, map, response, providerParams);
    }

    @Override
    public ContractResponse syncMerchantToHaike(String subMchId) {
        HaikeUnionSyncRequest request = new HaikeUnionSyncRequest();
        try {
            Map providerParams = providerTradeParamsService.getMerchantProviderParamsByPayMerchantId(subMchId);
            if (BeanUtil.getPropInt(providerParams, "payway") == TradeConstants.PAYWAY_ALIPAY2) {
                request.setMerch_no(BeanUtil.getPropString(providerParams, "provider_merchant_id"));
                HaikeUnionSyncRequest.ALiChannelInfo aliChannelInfo = new HaikeUnionSyncRequest.ALiChannelInfo();
                aliChannelInfo.setChannel_no(BeanUtil.getPropString(providerParams, "channel_no"));
                aliChannelInfo.setMerch_name(BeanUtil.getPropString(providerParams, "merchant_name"));
                Map contextParam = paramContextUtil.getParamContextByMerchantSn(BeanUtil.getPropString(providerParams, "merchant_sn"));
                String alias = AliCommonUtil.convertMerchantShortName(MapUtils.getMap(contextParam, "merchant"),
                        MapUtils.getMap(contextParam, "merchantBusinessLicense"),
                        contextParam, BeanUtil.getPropString(providerParams, "merchant_name"));
                if (org.springframework.util.StringUtils.isEmpty(alias)) {
                    alias = Utils.substring(BeanUtil.getPropString(providerParams, "merchant_name"), 20);
                } else {
                    alias = Utils.substring(StringFilter.filter(alias), 20);
                }
                aliChannelInfo.setMerch_short_name(alias);
                aliChannelInfo.setSub_merchant_no(BeanUtil.getPropString(providerParams, "pay_merchant_id"));
                aliChannelInfo.setMcc(BeanUtil.getPropString(providerParams, "ali_mcc"));
                List<HaikeUnionSyncRequest.ALiChannelInfo> aliChannelList = new ArrayList<>();
                aliChannelList.add(aliChannelInfo);
                request.setAli(aliChannelList);
            } else if (BeanUtil.getPropInt(providerParams, "payway") == TradeConstants.PAYWAY_WEIXIN) {
                request.setMerch_no(BeanUtil.getPropString(providerParams, "provider_merchant_id"));
                HaikeUnionSyncRequest.WxChannelInfo wxChannelInfo = new HaikeUnionSyncRequest.WxChannelInfo();
                wxChannelInfo.setChannel_no(BeanUtil.getPropString(providerParams, "channel_no"));
                wxChannelInfo.setMerch_name(BeanUtil.getPropString(providerParams, "merchant_name"));
                Map contextParam = paramContextUtil.getParamContextByMerchantSn(BeanUtil.getPropString(providerParams, "merchant_sn"));
                String alias = weiXinRuleBiz.getMerchantBusinessName(contextParam, BeanUtil.getPropString(providerParams, "merchant_name"));
                if (org.springframework.util.StringUtils.isEmpty(alias)) {
                    alias = Utils.substring(BeanUtil.getPropString(providerParams, "merchant_name"), 20);
                } else {
                    alias = Utils.substring(StringFilter.filter(alias), 20);
                }
                wxChannelInfo.setMerch_short_name(alias);
                wxChannelInfo.setSettle_id(BeanUtil.getPropString(providerParams, "wx_settlement_id"));
                List<HaikeUnionSyncRequest.WxChannelInfo> wxChannelInfoList = new ArrayList<>();
                wxChannelInfoList.add(wxChannelInfo);
                wxChannelInfo.setSub_merchant_no(BeanUtil.getPropString(providerParams, "pay_merchant_id"));
                request.setWx(wxChannelInfoList);
            }
            HaikeParam haikeParam = contractBaseBiz.buildContractParams("haike", HaikeParam.class);
            Map response = call(haikeContractUrl + "/front-api/merch/report-sync", JSONObject.parseObject(JSONObject.toJSONString(request)), haikeParam);
            String resultCode = BeanUtil.getPropString(response, "result_code");
            String resultMsg = BeanUtil.getPropString(response, "result_msg");
            ContractResponse contractResponse = new ContractResponse().setRequestParam((Map<String, Object>) JSONObject.parse(JSONObject.toJSONString(request))).setResponseParam(response)
                    .setMessage(resultMsg);
            if ("10000".equals(resultCode)) {
                contractResponse.setCode(200);
                if (request.getWx() != null) {
                    for (HaikeUnionSyncRequest.WxChannelInfo wxChannelInfo : request.getWx()) {
                        markSyncTagToParams(wxChannelInfo.getSub_merchant_no());
                    }
                }
                if (request.getAli() != null) {
                    for (HaikeUnionSyncRequest.ALiChannelInfo aLiChannelInfo : request.getAli()) {
                        markSyncTagToParams(aLiChannelInfo.getSub_merchant_no());
                    }
                }
                if (request.getBank() != null) {
                    for (HaikeUnionSyncRequest.BankChannelInfo bankChannelInfo : request.getBank()) {
                        markSyncTagToParams(bankChannelInfo.getBank_merch_no());
                    }
                }
            } else {
                contractResponse.setCode(errorInfoUtil.buildResultCode(ErrorInfoUtil.HAIKE_ERROR_INFO, resultCode));
            }
            return contractResponse;
        } catch (Exception e) {
            log.error("syncMerchantToHaike to haike failed payMerchantId {}", subMchId, e);
            chatBotUtil.sendMessageToContractWarnChatBot(String.format("haike 同步商户信息出现异常:%s,%s", subMchId, e.toString()));
            return new ContractResponse().setCode(Constant.RESULT_CODE_BIZ_EXCEPTION).setMessage(e.getMessage());
        }
    }


    @Override
    public ContractResponse syncTerminalToHaike(String merchantSn, String terminalId) {
        ContractResponse response = new ContractResponse();
        try {
            HaikeParam haikeParam = contractBaseBiz.buildContractParams("haike", HaikeParam.class);
            Map<String, Object> providerTerminal = terminalRepository.getProviderTerminalInfoByTerminalId(terminalId);
            Map acquirerParams = providerTradeParamsService.getMerchantProviderParamsByPayMerchantId(BeanUtil.getPropString(providerTerminal, "acquirer_merchant_id"));
            Map query = avro.shaded.com.google.common.collect.Maps.newHashMap();
            query.put(MerchantProviderParams.PROVIDER_MERCHANT_ID, BeanUtil.getPropString(acquirerParams, MerchantProviderParams.PROVIDER_MERCHANT_ID));
            query.put(MerchantProviderParams.PROVIDER, ProviderEnum.PROVIDER_HAIKE.getValue());
            ListResult providerParamsList = providerTradeParamsService.listMerchantProviderParams(null, query);
            HaikeTerminalRequest request = new HaikeTerminalRequest();
            request.setMerch_no(BeanUtil.getPropString(providerTerminal, "acquirer_merchant_id"));
            if (providerTerminal != null) {
                List<HaikeTerminalRequest.TerminalInfo> terminalInfoList = new ArrayList<>();
                HaikeTerminalRequest.TerminalInfo terminalInfo = new HaikeTerminalRequest.TerminalInfo();
                terminalInfo.setTerminal_id(BeanUtil.getPropString(providerTerminal, "provider_terminal_id"));
                terminalInfo.setSn(BeanUtil.getPropString(providerTerminal, "provider_terminal_id"));
                for (Map<String, Object> providerParams : providerParamsList.getRecords()) {
                    if (BeanUtil.getPropInt(providerParams, "payway") == PaywayEnum.ALIPAY.getValue()) {
                        terminalInfo.setAli_sub_merchant_no(BeanUtil.getPropString(providerParams, "pay_merchant_id"));
                    }
                    if (BeanUtil.getPropInt(providerParams, "payway") == PaywayEnum.WEIXIN.getValue()) {
                        terminalInfo.setWx_sub_merchant_no(BeanUtil.getPropString(providerParams, "pay_merchant_id"));
                    }
                    if (PaywayEnum.UNIONPAY.getValue() == (BeanUtil.getPropInt(providerParams, "payway"))) {
                        terminalInfo.setUnion_merchant_no(BeanUtil.getPropString(providerParams, "pay_merchant_id"));
                        terminalInfo.setCode("11");
                        Map merchant = merchantService.getMerchantBySn(merchantSn);
                        terminalInfo.setTerminal_address(Utils.substring(commonBiz.getAddressFromExtraForHaike(merchant), 30));
                    }
                }
                terminalInfoList.add(terminalInfo);
                request.setTerminal_info_list(terminalInfoList);
                response = syncTerminalToHaike(request, haikeParam);
            }
        } catch (Exception e) {
            logger.error("海科同步子商户号信息异常:" + e.getMessage());
            return new ContractResponse().setCode(460).setMessage(e.getMessage());
        }
        return response;
    }


    @Override
    public ContractResponse syncTerminalToHaike(String merchantSn, String terminalId, Integer payway) {
        ContractResponse response = new ContractResponse();
        try {
            HaikeParam haikeParam = contractBaseBiz.buildContractParams("haike", HaikeParam.class);
            Map<String, Object> providerTerminal = terminalRepository.getProviderTerminalInfoByTerminalId(terminalId);
            Map acquirerParams = providerTradeParamsService.getMerchantProviderParamsByPayMerchantId(BeanUtil.getPropString(providerTerminal, "acquirer_merchant_id"));
            Map query = avro.shaded.com.google.common.collect.Maps.newHashMap();
            query.put(MerchantProviderParams.PROVIDER_MERCHANT_ID, BeanUtil.getPropString(acquirerParams, MerchantProviderParams.PROVIDER_MERCHANT_ID));
            query.put(MerchantProviderParams.PROVIDER, ProviderEnum.PROVIDER_HAIKE.getValue());
            ListResult providerParamsList = providerTradeParamsService.listMerchantProviderParams(null, query);
            HaikeTerminalRequest request = new HaikeTerminalRequest();
            request.setMerch_no(BeanUtil.getPropString(providerTerminal, "acquirer_merchant_id"));
            if (providerTerminal != null) {
                List<HaikeTerminalRequest.TerminalInfo> terminalInfoList = new ArrayList<>();
                HaikeTerminalRequest.TerminalInfo terminalInfo = new HaikeTerminalRequest.TerminalInfo();
                terminalInfo.setTerminal_id(BeanUtil.getPropString(providerTerminal, "provider_terminal_id"));
                terminalInfo.setSn(BeanUtil.getPropString(providerTerminal, "provider_terminal_id"));
                for (Map<String, Object> providerParams : providerParamsList.getRecords()) {
                    if (PaywayEnum.ALIPAY.getValue().intValue() == payway && PaywayEnum.ALIPAY.getValue().intValue() == BeanUtil.getPropInt(providerParams, "payway")) {
                        terminalInfo.setAli_sub_merchant_no(BeanUtil.getPropString(providerParams, "pay_merchant_id"));
                        break;
                    }
                    if (PaywayEnum.WEIXIN.getValue().intValue() == payway && PaywayEnum.WEIXIN.getValue() == BeanUtil.getPropInt(providerParams, "payway")) {
                        terminalInfo.setWx_sub_merchant_no(BeanUtil.getPropString(providerParams, "pay_merchant_id"));
                        break;
                    }
                    if (PaywayEnum.UNIONPAY.getValue().intValue() == payway && PaywayEnum.UNIONPAY.getValue() == BeanUtil.getPropInt(providerParams, "payway")) {
                        terminalInfo.setUnion_merchant_no(BeanUtil.getPropString(providerParams, "pay_merchant_id"));
                        terminalInfo.setCode("11");
                        Map merchant = merchantService.getMerchantBySn(merchantSn);
                        terminalInfo.setTerminal_address(Utils.substring(commonBiz.getAddressFromExtraForHaike(merchant), 30));
                        break;
                    }
                }
                terminalInfoList.add(terminalInfo);
                request.setTerminal_info_list(terminalInfoList);
                response = syncTerminalToHaike(request, haikeParam);
            }
        } catch (Exception e) {
            logger.error("海科同步子商户号信息异常:" + e.getMessage());
            return new ContractResponse().setCode(460).setMessage(e.getMessage());
        }
        return response;
    }

    @Override
    public ContractResponse syncTerminalToHaike(HaikeTerminalRequest request, HaikeParam haikeParam) {
        try {
            Map response = call(haikeContractUrl + "/front-api/terminal/terminalSynchronous", JSONObject.parseObject(JSONObject.toJSONString(request)), haikeParam);
            String resultCode = BeanUtil.getPropString(response, "result_code");
            String resultMsg = BeanUtil.getPropString(response, "result_msg");
            ContractResponse contractResponse = new ContractResponse().setRequestParam((Map<String, Object>) JSONObject.parse(JSONObject.toJSONString(request))).setResponseParam(response)
                    .setMessage(resultMsg);
            if ("SUCCESS".equals(resultCode)) {
                contractResponse.setCode(200);
            } else {
                contractResponse.setCode(errorInfoUtil.buildResultCode(ErrorInfoUtil.HAIKE_ERROR_INFO, resultCode));
            }
            return contractResponse;
        } catch (Exception e) {
            log.error("syncTerminalToHaike to haike failed payMerchantId {}", request.getMerch_no(), e);
            chatBotUtil.sendMessageToContractWarnChatBot(String.format("haike 商户同步终端出现异常:%s,%s", request.getMerch_no(), e.toString()));
            return new ContractResponse().setCode(Constant.RESULT_CODE_SYSTEM_EXCEPTION).setMessage(e.getMessage());
        }
    }


    @Override
    public ContractResponse merchantAffiliation(MerchantAffiliationRequest request) {
        Map<String, Object> req = new HashMap();
        try {
            HaikeParam haikeParam = contractBaseBiz.buildContractParams("haike", HaikeParam.class);
            Map providerParams = providerTradeParamsService.getMerchantProviderParamsByPayMerchantId(request.getSubMchId());
            if (providerParams == null) {
                throw new ContractBizException("未查到该子商户号");
            }
            Map<String, Object> acquirerParam = providerTradeParamsService.getMerchantProviderParams(request.getMerchantSn(), "haike", PaywayEnum.ACQUIRER.getValue(), String.valueOf(MerchantProviderParams.PROVIDER_HAIKE));
            if (acquirerParam == null) {
                throw new ContractBizException("加盟商商户还未入网");
            }
            Random random = new Random();
            req.put("application_no", String.valueOf(System.currentTimeMillis()) + (random.nextInt(900) + 100));
            req.put("brand_merch_no", BeanUtil.getPropString(providerParams, MerchantProviderParams.PROVIDER_MERCHANT_ID));
            req.put("brand_sub_merch_no", BeanUtil.getPropString(providerParams, MerchantProviderParams.PAY_MERCHANT_ID));
            if (request.getPayway().intValue() == PaywayEnum.WEIXIN.getValue()) {
                req.put("brand_sub_merch_type", "WX");
            } else if (request.getPayway().intValue() == PaywayEnum.ALIPAY.getValue()) {
                req.put("brand_sub_merch_type", "ALI");
            }
            req.put("merch_no", BeanUtil.getPropString(acquirerParam, MerchantProviderParams.PROVIDER_MERCHANT_ID));
            Map response = call(haikeContractUrl + "/front-api/merch/affiliation/save", JSONObject.parseObject(JSONObject.toJSONString(req)), haikeParam);
            String resultCode = BeanUtil.getPropString(response, "result_code");
            String resultMsg = BeanUtil.getPropString(response, "result_msg");
            ContractResponse contractResponse = new ContractResponse().setRequestParam((Map<String, Object>) JSONObject.parse(JSONObject.toJSONString(request))).setResponseParam(response)
                    .setMessage(resultMsg);
            if ("10000".equals(resultCode)) {
                contractResponse.setCode(200);
            } else {
                contractResponse.setCode(errorInfoUtil.buildResultCode(ErrorInfoUtil.HAIKE_ERROR_INFO, resultCode));
            }
            return contractResponse;
        } catch (Exception e) {
            log.error("merchantAffiliation to haike failed payMerchantId {}", null, e);
            return new ContractResponse().setCode(Constant.RESULT_CODE_SYSTEM_EXCEPTION).setMessage(e.getMessage());
        }
    }

    @Override
    public ContractResponse addWxTermInfo(AddTermInfoDTO dto, UnionWeixinParam weixinParam) {
        final ContractResponse response = wxTermInfoOperate(dto, weixinParam, "00");
        Map<String, Object> responseParam = response.getResponseParam();
        String result_code = WosaiMapUtils.getString(responseParam, "result_code");
        String err_code_des = WosaiMapUtils.getString(responseParam, "err_code_des");
        // 如果报错   该商户已绑定该终端，请进行状态变更 [UP0200019]
        // 直接进行一次更新
        if ("FAIL".equals(result_code) &&
                err_code_des.contains("该商户已绑定该终端，请进行状态变更")) {
            return wxTermInfoOperate(dto, weixinParam, "01");
        } else {
            return response;
        }
    }

    private void markSyncTagToParams(String subMchId) {
        Map params = providerTradeParamsService.getMerchantProviderParamsByPayMerchantId(subMchId);
        Map extra = WosaiMapUtils.getMap(params, MerchantProviderParams.EXTRA, new HashMap());
        extra.put("syncStatus", "true");
        Map updateMerchantProviderParams = CollectionUtil.hashMap(
                MerchantProviderParams.EXTRA, extra
        );
        providerTradeParamsService.updateMerchantProviderParamExtraBySubMchId(subMchId, updateMerchantProviderParams);

    }

    /**
     * 构建微信绑定信息
     *
     * @param dto         绑定参数
     * @param weixinParam 银联微信参数
     * @param operationId 本次操作标识，取值范围： 00：新增； 01：修改； 02：注销；（注销时，仅需上送 device_id 字段）
     * @return
     */
    private ContractResponse wxTermInfoOperate(BaseTermInfoDTO dto, UnionWeixinParam weixinParam, String operationId) {
        Map<String, Object> request = avro.shaded.com.google.common.collect.Maps.newHashMap();
        Map<String, Object> response = avro.shaded.com.google.common.collect.Maps.newHashMap();
        Map<String, Object> tradeParams = avro.shaded.com.google.common.collect.Maps.newHashMap();
        int code = 200;
        String message = "";
        try {
            //1、组装请求参数
            final WxTermInfoRequest wxTermInfoRequest = contractWith259Biz.buildWXTermInfoRequest(dto, operationId);
            wxTermInfoRequest.setAppid(weixinParam.getAppid())
                    .setMchId(weixinParam.getMch_id());
            request = JSON.parseObject(JSON.toJSONString(wxTermInfoRequest));
            log.info("call union wxTermInfoRequest: {}", JSON.toJSONString(request));
            response = newUnionBiz.callWeixin("/terminal/collect", weixinParam, request);
            log.info("call union termInfoResponse :{}  ", JSON.toJSONString(response));
            String return_code = WosaiMapUtils.getString(response, "return_code");
            String return_msg = WosaiMapUtils.getString(response, "return_msg");
            String result_code = WosaiMapUtils.getString(response, "result_code");
            if (!"SUCCESS".equals(return_code) || !"SUCCESS".equals(result_code)) {
                String err_code_des = WosaiMapUtils.getString(response, "err_code_des");
                if (StringUtil.empty(err_code_des)) {
                    err_code_des = return_msg;
                }
                message = err_code_des;
                throw new ContractException(errorInfoUtil.buildResultCode(ErrorInfoUtil.CONTRACT_MESSAGE_TL_WECHAT, err_code_des), message);
            }
        } catch (ContractException e) {
            log.error("haike termInfo error", e);
            code = e.getCode();
            if (WosaiStringUtils.isEmpty(message)) {
                message = e.getMessage();
            }
        } catch (Exception e) {
            log.error("haike termInfo error", e);
            code = Constant.RESULT_CODE_SYSTEM_EXCEPTION;
            message = e.getMessage();
            if (e.getCause() != null) {
                message = e.getCause().getMessage();
            }
        }
        ContractResponse result = new ContractResponse();
        result.setCode(code);
        result.setMessage(message);
        result.setRequestParam(request);
        result.setResponseParam(response);
        result.setTradeParam(tradeParams);
        return result;
    }


    @Override
    public ContractResponse updateWxTermInfo(UpdateTermInfoDTO dto, UnionWeixinParam weixinParam) {
        final ContractResponse response = wxTermInfoOperate(dto, weixinParam, "01");
        return response;
    }

    @Override
    public ContractResponse LogOutWxTermInfo(LogOutTermInfoDTO dto, UnionWeixinParam weixinParam) {
        final ContractResponse response = wxTermInfoOperate(dto, weixinParam, "02");
        return response;
    }

    @Override
    public ContractResponse addAliTermInfo(AddTermInfoDTO dto, UnionAlipayParam alipayParam) {
        final ContractResponse response = aliTermInfoOperate(dto, alipayParam, "00");
        Map<String, Object> responseParam = response.getResponseParam();
        // 如果报错   该商户已绑定该终端，请进行状态变更 [UP0200019]
        // 直接进行一次更新
        if (!"10000".equals(responseParam.get("code")) &&
                "up.terminal_acquire_duplicate".equals(responseParam.get("sub_code"))) {
            return aliTermInfoOperate(dto, alipayParam, "01");
        } else {
            return response;
        }
    }

    /**
     * 构建阿里终端绑定信息
     *
     * @param dto         绑定参数
     * @param alipayParam 银联支付宝参数
     * @param operationId 本次操作标识，取值范围： 00：新增； 01：修改； 02：注销；（注销时，仅需上送 device_id 字段）
     * @return
     */
    private ContractResponse aliTermInfoOperate(BaseTermInfoDTO dto, UnionAlipayParam alipayParam, String operationId) {
        Map request = avro.shaded.com.google.common.collect.Maps.newHashMap();
        Map<String, Object> response = avro.shaded.com.google.common.collect.Maps.newHashMap();
        Map<String, Object> tradeParams = avro.shaded.com.google.common.collect.Maps.newHashMap();
        int code = 200;
        String message = "";
        String msg = "";
        String resCode = "";
        try {
            //1、组装请求参数
            final AliTermInfoRequest aliTermInfoRequest = contractWith259Biz.buildAliTermInfoRequest(dto, alipayParam.getProvider(), operationId);
            request = newUnionBiz.buildAliCommonParam("up.merchant.terminal.information.acquire", alipayParam);
            request.put("biz_content", JSON.toJSONString(aliTermInfoRequest));
            log.info("call haike aliTermInfoOperate request {}", JSON.toJSONString(request));
            response = newUnionBiz.callAlipay(alipayParam, request);
            log.info("call haike aliTermInfoOperate response {}", JSON.toJSONString(response));
            if (response != null) {
                msg = WosaiMapUtils.getString(response, "msg");
                resCode = WosaiMapUtils.getString(response, "code");
                if (!"Success".equals(msg) || !"10000".equals(resCode)) {
                    String subCode = WosaiMapUtils.getString(response, "sub_code");
                    message = WosaiMapUtils.getString(response, "sub_msg");
                    throw new ContractException(errorInfoUtil.buildResultCode(ErrorInfoUtil.CONTRACT_MESSAGE_TL_ALIPAY, subCode), message);
                }
            }
        } catch (ContractException e) {
            log.error("haike aliTermInfoOperateWithParams error", e);
            code = e.getCode();
            if (WosaiStringUtils.isEmpty(message)) {
                message = e.getMessage();
            }
        } catch (Exception e) {
            log.error("haike aliTermInfoOperateWithParams error", e);
            code = Constant.RESULT_CODE_SYSTEM_EXCEPTION;
            message = e.getMessage();
            if (e.getCause() != null) {
                message = e.getCause().getMessage();
            }
        }
        ContractResponse result = new ContractResponse();
        result.setCode(code);
        result.setMessage(message);
        result.setRequestParam(request);
        result.setResponseParam(response);
        result.setTradeParam(tradeParams);
        return result;
    }


    @Override
    public ContractResponse updateAliTermInfo(UpdateTermInfoDTO dto, UnionAlipayParam alipayParam) {
        final ContractResponse response = aliTermInfoOperate(dto, alipayParam, "01");
        return response;
    }

    @Override
    public ContractResponse LogOutAliTermInfo(LogOutTermInfoDTO dto, UnionAlipayParam alipayParam) {
        final ContractResponse response = aliTermInfoOperate(dto, alipayParam, "02");
        return response;
    }

    @Override
    public Map weixinSubdevConfig(WeixinConfig weixinConfig, HaikeParam haikeParam) {
        String subMchId = weixinConfig.getWeixinMchId();
        boolean isSuccess = true;
        Map<String, String> resultMap = new HashMap();
        List<String> errorMessageList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(weixinConfig.getAppidConfigs())) {
            for (WeixinAppidConfig appidConfig : weixinConfig.getAppidConfigs()) {
                try {
                    //2.1 appid
                    if (WosaiStringUtils.isNotEmpty(appidConfig.getSub_appid())) {
                        Map param = CollectionUtil.hashMap("sub_mch_id", subMchId,
                                "sub_appid", appidConfig.getSub_appid()
                        );
                        Map response = call(haikeContractUrl + "/front-api/wxappid/add", param, haikeParam);
                        if ("10000".equals(BeanUtil.getPropString(response, "result_code"))) {
                            Map attach = JSONObject.parseObject(BeanUtil.getPropString(response, "attach"));
                            String code = WosaiMapUtils.getString(attach, UnionBusinessFileds.RESULT_CODE);
                            if (!UnionConstant.RETURN_CODE_SUCCESS.equalsIgnoreCase(code) && !Utils.alreadyConfig(attach.toString())) {
                                errorMessageList.add(appidConfig.getSub_appid() + ":添加失败," + BeanUtil.getPropString(attach, UnionBusinessFileds.ERR_CODE_DES));
                                isSuccess = false;
                            }
                        } else {
                            isSuccess = false;
                            errorMessageList.add(appidConfig.getSub_appid() + ":添加失败," + BeanUtil.getPropString(response, "result_msg"));
                        }
                    }
                } catch (Exception e) {
                    errorMessageList.add(appidConfig.getSub_appid() + ":添加失败," + e.getMessage());
                    logger.error("海科微信参数配置异常:" + e.getMessage());
                }
            }
        }
        if (WosaiCollectionUtils.isNotEmpty(weixinConfig.getPayAuthPath())) {
            for (String path : weixinConfig.getPayAuthPath()) {
                try {
                    //2.1 支付目录
                    if (WosaiStringUtils.isNotEmpty(path)) {
                        Map param = CollectionUtil.hashMap("sub_mch_id", subMchId,
                                "jsapi_path", path
                        );
                        Map response = call(haikeContractUrl + "/front-api/wxappid/add", param, haikeParam);
                        if ("10000".equals(BeanUtil.getPropString(response, "result_code"))) {
                            Map attach = JSONObject.parseObject(BeanUtil.getPropString(response, "attach"));
                            String code = WosaiMapUtils.getString(attach, UnionBusinessFileds.RESULT_CODE);
                            if (!UnionConstant.RETURN_CODE_SUCCESS.equalsIgnoreCase(code) && !Utils.alreadyConfig(attach.toString())) {
                                errorMessageList.add(path + ":添加失败," + BeanUtil.getPropString(attach, UnionBusinessFileds.ERR_CODE_DES));
                                isSuccess = false;
                            }
                        } else {
                            errorMessageList.add(path + ":添加失败," + BeanUtil.getPropString(response, "result_msg"));
                            isSuccess = false;
                        }
                    }
                } catch (Exception e) {
                    errorMessageList.add(path + ":添加失败," + e.getMessage());
                    logger.error("海科微信参数配置异常:" + e.getMessage());
                }
            }
        }
        if (isSuccess) {
            resultMap.put("result_code", "1");
            resultMap.put("message", "配置成功");
        } else {
            resultMap.put("result_code", "0");
            resultMap.put("message", JSONObject.toJSONString(errorMessageList));
        }
        return resultMap;
    }

    /**
     * 微信子商户号相关配置
     *
     * @param:
     * @return:
     * @date: 17:34
     */
    public SubdevConfigResp queryWeixinSubdevConfig(String subMchId, HaikeParam haikeParam) {
        Map<String, String> request = new HashMap();
        SubdevConfigResp resp = new SubdevConfigResp();
        try {
            request.put("sub_mch_id", subMchId);
            Map response = call(haikeContractUrl + "/front-api/wxappid/query", request, haikeParam);
            if ("10000".equals(BeanUtil.getPropString(response, "result_code"))) {
                Map result = JSONObject.parseObject(BeanUtil.getPropString(response, "attach"));
                if ("SUCCESS".equals(BeanUtil.getPropString(result, UnionBusinessFileds.RESULT_CODE))) {
                    resp.setJsapi_path_list(JSONObject.parseArray(BeanUtil.getPropString(result, "jsapi_path_list"), String.class));
                    resp.setAppid_config_list(JSONObject.parseArray(BeanUtil.getPropString(result, "appid_config_list"), SubdevConfigResp.AppidConfig.class));
                }
            }
        } catch (Exception e) {
            logger.error("海科微信参数配置查询异常:" + e.getMessage());
        }
        return resp;
    }

    @Override
    public ApplySpecialFeeRateResponse applyFeeRate(ApplySpecialFeeRateParam param, HaikeActivityParam haikeActivityParam) {
        ApplySpecialFeeRateResponse res = new ApplySpecialFeeRateResponse();
        Map<String, Object> request = new HashMap<>();
        ApplySpecialFeeRateParam.Activity_detail activityDetail = param.getActivity_detail();
        HaikeParam haikeParam = new HaikeParam();
        BeanUtils.copyProperties(haikeActivityParam, haikeParam);
        for (ApplySpecialFeeRateParam.Material material : activityDetail.getActivity_apply_information()) {
            List<String> cur = Lists.newArrayList();
            for (String info : material.getApply_material_information()) {
                cur.add(haikeBiz.uploadPic(info, "微信活动报名图片", haikeParam));
            }
            material.setApply_material_information(cur);
        }

        request.put("agent_apply_no", haikeActivityParam.getAgentApplyNo());
        request.put("merch_no", haikeActivityParam.getMerchNo());
        request.put("channel_no", haikeActivityParam.getChannel_no());
        request.put("sub_mch_id", param.getSub_mchid());
        request.put("activity_detail", JSONObject.parseObject(JSONObject.toJSONString(activityDetail), JSON.class));
        res.setRequest(JSONObject.toJSONString(request));
        Map<String, String> extraMap = CollectionUtil.hashMap("agent_apply_no", haikeActivityParam.getAgentApplyNo());
        res.setExtra(JSONObject.toJSONString(extraMap));
        try {
            Map response = call(haikeContractUrl + "/front-api/wx-activity/application", request, haikeParam);
            if ("10000".equals(BeanUtil.getPropString(response, "result_code"))) {
                Map result = JSONObject.parseObject(BeanUtil.getPropString(response, "attach"));
                String applicationId = BeanUtil.getPropString(result, "application_id");
                if (!StringUtils.isEmpty(applicationId)) {
                    res.setApplication_id(applicationId);
                    res.setCode("200");
                } else {
                    res.setCode("400");
                    res.setMessage(WosaiMapUtils.getString(result, "message"));
                }
            } else {
                res.setCode("500");
            }
            res.setResposne(JSONObject.toJSONString(response));
        } catch (Exception e) {
            res.setCode("500");
            res.setMessage(e.getMessage());
        }
        return res;
    }

    @Override
    public ApplySpecialFeeRateResponse modifyRateApply(ModifySpecialFeeRateParam param, HaikeActivityParam haikeActivityParam) {
        ApplySpecialFeeRateResponse applySpecialFeeRateResponse = new ApplySpecialFeeRateResponse();
        Map<String, Object> request = new HashMap<>();
        HaikeParam haikeParam = new HaikeParam();
        BeanUtils.copyProperties(haikeActivityParam, haikeParam);
        ModifySpecialFeeRateParam.Activity_detail_modification activityDetail = param.getActivity_detail_modification();
        for (ModifySpecialFeeRateParam.Material material : activityDetail.getActivity_apply_information()) {
            List<String> cur = Lists.newArrayList();
            for (String info : material.getApply_material_information()) {
                cur.add(haikeBiz.uploadPic(info, "微信活动报名图片", haikeParam));
            }
            material.setApply_material_information(cur);
        }
        request.put("application_id", param.getApplication_id());
        request.put("activity_detail_modification", JSONObject.parseObject(JSONObject.toJSONString(activityDetail), JSON.class));
        applySpecialFeeRateResponse.setRequest(JSONObject.toJSONString(request));
        try {
            Map response = call(haikeContractUrl + "/front-api/wx-activity/modify", request, haikeParam);
            if ("10000".equals(BeanUtil.getPropString(response, "result_code"))) {
                Map result = JSONObject.parseObject(BeanUtil.getPropString(response, "attach"));
                String applicationId = BeanUtil.getPropString(result, "application_id");
                if (!StringUtils.isEmpty(applicationId)) {
                    applySpecialFeeRateResponse.setCode("200");
                    applySpecialFeeRateResponse.setApplication_id(applicationId);
                } else {
                    applySpecialFeeRateResponse.setCode("400");
                }
            } else {
                applySpecialFeeRateResponse.setCode("400");
                applySpecialFeeRateResponse.setMessage(BeanUtil.getPropString(response, "result_msg"));
                applySpecialFeeRateResponse.setApplication_id(param.getApplication_id());
            }
            applySpecialFeeRateResponse.setResposne(JSONObject.toJSONString(response));
        } catch (Exception e) {
            applySpecialFeeRateResponse.setCode("500");
        }
        return applySpecialFeeRateResponse;
    }

    @Override
    public ApplySpecialFeeRateQueryResponse queryRateApplyStatus(String applicationId, HaikeParam haikeParam) throws Exception {
        ApplySpecialFeeRateQueryResponse applySpecialFeeRateQueryResponse = new ApplySpecialFeeRateQueryResponse();
        Map<String, String> request = new HashMap<>();
        request.put("application_id", applicationId);
        Map response = call(haikeContractUrl + "/front-api/wx-activity/query", request, haikeParam);
        if ("10000".equals(BeanUtil.getPropString(response, "result_code"))) {
            Map result = JSONObject.parseObject(BeanUtil.getPropString(response, "attach"));
            applySpecialFeeRateQueryResponse.setStatus_code(200);
            String applicationState = WosaiMapUtils.getString(result, "application_state");
            String rejectParameter = WosaiMapUtils.getString(result, "reject_parameter");
            String rejectReason = WosaiMapUtils.getString(result, "reject_reason");
            String passTime = WosaiMapUtils.getString(result, "pass_time");
            applySpecialFeeRateQueryResponse.setApplication_state(applicationState);
            applySpecialFeeRateQueryResponse.setReject_parameter(rejectParameter);
            applySpecialFeeRateQueryResponse.setReject_reason(rejectReason);
            applySpecialFeeRateQueryResponse.setPass_time(passTime);
        } else {
            if (Objects.equals(BeanUtil.getPropString(response, "return_code"), "FAIL")
                    && Objects.equals(BeanUtil.getPropString(response, "return_msg"), "重复请求")) {
                applySpecialFeeRateQueryResponse.setStatus_code(200);
                applySpecialFeeRateQueryResponse.setApplication_state("APPLICATION_STATE_AUDITING");
            } else {
                applySpecialFeeRateQueryResponse.setStatus_code(400);
            }
        }
        return applySpecialFeeRateQueryResponse;
    }

    @Override
    public ApplySpecialFeeRateResponse syncAlipayUniversityActivitySuccessResult(AlipaySyncSpecialFeeRateParam param, HaikeActivityParam haikeActivityParam) {
        ApplySpecialFeeRateResponse res = new ApplySpecialFeeRateResponse();
        HaikeAlipayActivitySyncRequest syncRequest = new HaikeAlipayActivitySyncRequest();
        syncRequest.setIndustryCode(param.getMcc())
                .setAgentApplyNo(haikeActivityParam.getAgentApplyNo())
                .setActivityType("INDUSTRY_SPECIAL")
                .setMerchNo(haikeActivityParam.getMerchNo())
                .setSubMchId(param.getSubMchId())
                .setChannelNo(haikeActivityParam.getChannel_no());
        HaikeParam haikeParam = new HaikeParam();
        BeanUtils.copyProperties(haikeActivityParam, haikeParam);
        try {
            Map request = JSONObject.parseObject(JSONObject.toJSONString(syncRequest), Map.class);
            Map response = call(haikeContractUrl + "/front-api/ali-activity/synActivity", request, haikeParam);
            if ("10000".equals(BeanUtil.getPropString(response, "result_code"))) {
                String status = BeanUtil.getPropString(response, "status");
                if (Objects.equals(status, "1") || Objects.equals(status, "2") || Objects.equals(status, "3")) {
                    res.setCode("200");
                    String resultMsg = BeanUtil.getPropString(response, "result_msg");
                    res.setMessage(resultMsg);
                } else {
                    res.setCode("400");
                    res.setMessage("status=" + status + "，业务状态有误");
                }
            } else {
                res.setCode("500");
            }
            res.setRequest(JSONObject.toJSONString(request));
            res.setResposne(JSONObject.toJSONString(response));
        } catch (Exception e) {
            res.setCode("500");
        }
        return res;
    }


    /**
     * 商户终端和商户信息同步
     *
     * @param subMchId
     * @return
     */
    @Override
    public ContractResponse syncTerminalAndMerchant(String subMchId) {
        ContractResponse response = new ContractResponse();
        try {
            HaikeParam haikeParam = contractBaseBiz.buildContractParams("haike", HaikeParam.class);
            Map<String, Object> providerParams = providerTradeParamsService.getMerchantProviderParamsByPayMerchantId(subMchId);
            if (providerParams == null) {
                throw new ContractBizException("未找到对应的子商户号");
            }
            Map acquirerParams = providerTradeParamsService.getProviderParamsByProviderMerchantIdAndPayway(BeanUtil.getPropString(providerParams, MerchantProviderParams.PROVIDER_MERCHANT_ID), PaywayEnum.ACQUIRER.getValue());
            List<Map<String, Object>> providerTerminal =
                    terminalRepository.getProviderTerminalInfoByMerchant(BeanUtil.getPropString(providerParams, MerchantProviderParams.MERCHANT_SN),
                            BeanUtil.getPropString(providerParams, MerchantProviderParams.PROVIDER)
                            , BeanUtil.getPropString(acquirerParams, MerchantProviderParams.PAY_MERCHANT_ID));
            HaikeTerminalRequest request = new HaikeTerminalRequest();
            request.setMerch_no(BeanUtil.getPropString(acquirerParams, MerchantProviderParams.PAY_MERCHANT_ID));
            if (CollectionUtils.isNotEmpty(providerTerminal)) {
                for (Map terminal : providerTerminal) {
                    List<HaikeTerminalRequest.TerminalInfo> terminalInfoList = new ArrayList<>();
                    HaikeTerminalRequest.TerminalInfo terminalInfo = new HaikeTerminalRequest.TerminalInfo();
                    terminalInfo.setTerminal_id(BeanUtil.getPropString(terminal, "provider_terminal_id"));
                    terminalInfo.setSn(BeanUtil.getPropString(terminal, "provider_terminal_id"));
                    if (BeanUtil.getPropInt(providerParams, "payway") == PaywayEnum.ALIPAY.getValue()) {
                        terminalInfo.setAli_sub_merchant_no(BeanUtil.getPropString(providerParams, "pay_merchant_id"));
                    }
                    if (BeanUtil.getPropInt(providerParams, "payway") == PaywayEnum.WEIXIN.getValue()) {
                        terminalInfo.setWx_sub_merchant_no(BeanUtil.getPropString(providerParams, "pay_merchant_id"));
                    }
                    if (PaywayEnum.UNIONPAY.getValue() == (BeanUtil.getPropInt(providerParams, "payway"))) {
                        terminalInfo.setUnion_merchant_no(BeanUtil.getPropString(providerParams, "pay_merchant_id"));
                        terminalInfo.setCode("11");
                        Map merchant = merchantService.getMerchantBySn(BeanUtil.getPropString(terminal, "merchant_sn"));
                        terminalInfo.setTerminal_address(Utils.substring(commonBiz.getAddressFromExtraForHaike(merchant), 30));
                    }
                    terminalInfoList.add(terminalInfo);
                    request.setTerminal_info_list(terminalInfoList);

                    response = syncTerminalToHaike(request, haikeParam);
                    if (!response.isSuccess()) {
                        syncMerchantToHaike(subMchId);
                        response = syncTerminalToHaike(request, haikeParam);
                        if (!response.isSuccess()) {
                            return response;
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("海科同步子商户号信息异常:" + e.getMessage());
            return new ContractResponse().setCode(460).setMessage(e.getMessage());
        }
        return response;
    }


    /**
     * 开通海科饭卡支付
     *
     * @param payMerchantId 海科商户号
     * @param fileName      文件名
     * @param fileUrl       文件路径
     * @param haikeParam    海科参数
     * @return 开通结果
     */
    @Override
    public ContractResponse openHaikeFoodCard(String payMerchantId, String fileName, String fileUrl, HaikeParam haikeParam) {
        // 上传文件并获取文件ID
        String fileId = uploadFile(fileName, fileUrl, haikeParam);
        if (org.apache.commons.lang3.StringUtils.isBlank(fileId)) {
            log.error("haike upload file error, payMerchantId:{}, fileName:{}, fileUrl:{}", payMerchantId, fileName, fileUrl);
            return ContractResponse.builder().code(500).message("附件上传失败").build();
        }
        Map<String, Object> response = Maps.newHashMap();
        Map<String, Object> tradeParams = Maps.newHashMap();
        int code = 200;
        String message = "";
        try {
            HaikeFoodCardRequest request = new HaikeFoodCardRequest();
            Random random = new Random();
            request.setAgentApplyNo(String.valueOf(System.currentTimeMillis()) + (random.nextInt(900) + 100));
            request.setBizType(HaikeFoodCardRequest.BIZ_TYPE);
            request.setMerchNo(payMerchantId);
            HaikeFoodCardRequest.BizInfo bizInfo = new HaikeFoodCardRequest.BizInfo();
            bizInfo.setFile_id(fileId);
            request.setBizInfo(bizInfo);
            Map<String, Object> requestMap = JSON.parseObject(JSON.toJSONString(request));
            response = call(haikeContractUrl + "/front-api/common-biz/apply", requestMap, haikeParam);
            String resultCode = BeanUtil.getPropString(response, "result_code");
            String resultMsg = BeanUtil.getPropString(response, "result_msg");
            if ("10000".equals(resultCode)) {
                message = "开通成功";
            } else {
                code = errorInfoUtil.buildResultCode(ErrorInfoUtil.HAIKE_ERROR_INFO, resultCode);
                message = resultMsg;
            }
            ContractResponse result = new ContractResponse();
            result.setCode(code);
            result.setMessage(message);
            result.setRequestParam(requestMap);
            result.setResponseParam(response);
            result.setTradeParam(tradeParams);
            return result;
        } catch (ContractException e) {
            log.error("haike open food card error, payMerchantId:{}", payMerchantId, e);
            code = errorInfoUtil.buildResultCode(ErrorInfoUtil.HAIKE_ERROR_INFO, String.valueOf(e.getCode()));
            if (WosaiStringUtils.isEmpty(message)) {
                message = e.getMessage();
            }
        } catch (Exception e) {
            log.error("haike open food card error, payMerchantId:{}", payMerchantId, e);
            code = Constant.RESULT_CODE_SYSTEM_EXCEPTION;
            message = e.getMessage();
            if (e.getCause() != null) {
                message = e.getCause().getMessage();
            }
        }
        ContractResponse result = new ContractResponse();
        result.setCode(code);
        result.setMessage(message);
        result.setRequestParam(Maps.newHashMap());
        result.setResponseParam(response);
        result.setTradeParam(tradeParams);
        return result;
    }

    private String uploadFile(String fileName, String fileUrl, HaikeParam haikeParam) {
        try {
            String base64String = ossBase64Converter.urlToBase64(fileUrl);
            if (org.apache.commons.lang3.StringUtils.isBlank(base64String)) {
                throw new ContractBizException("文件转换为Base64编码为空");
            }
            HaikeFileUploadRequest request = new HaikeFileUploadRequest();
            request.setFileName(fileName);
            request.setFile(base64String);
            Map<String, Object> requestMap = JSON.parseObject(JSON.toJSONString(request));
            Map<String, Object> response = call(haikeContractUrl + "/front-api/file/upload", requestMap, haikeParam);
            log.info("文件上传响应, fileName:{}, fileUrl:{}: {}", fileName, fileUrl, response);
            String resultCode = BeanUtil.getPropString(response, "result_code");
            if (!"10000".equals(resultCode)) {
                throw new ContractBizException("文件上传失败: " + BeanUtil.getPropString(response, "result_msg"));
            }
            return BeanUtil.getPropString(response, "file_id");
        } catch (Exception e) {
            log.error("文件上传异常,fileName:{}, fileUrl:{} : {}", fileName, fileUrl, e.getMessage(), e);
            throw new ContractBizException("文件上传异常: " + e.getMessage());
        }
    }
}
