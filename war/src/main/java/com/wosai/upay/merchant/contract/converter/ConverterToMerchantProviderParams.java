package com.wosai.upay.merchant.contract.converter;


import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import com.wosai.upay.merchant.contract.constant.ums.UmsAppTypeEnum;
import com.wosai.upay.merchant.contract.model.MerchantProviderParams;
import com.wosai.upay.merchant.contract.model.chinaums.response.ApplyQryResponse;
import com.wosai.upay.merchant.contract.model.provider.ChannelParam;
import com.wosai.upay.merchant.contract.model.provider.ChinaUmsParam;

import java.util.List;
import java.util.Map;

import static com.wosai.upay.merchant.contract.constant.TradeParamConstant.TRADE;

/**
 * @Description:
 * <AUTHOR>
 * @Date: 2021/3/17 10:51 上午
 */
public class ConverterToMerchantProviderParams {


    public static MerchantProviderParamsDto umsResponseToProviderParams(ApplyQryResponse applyQryResponse, ContractSubTask contractSubTask, ChinaUmsParam chinaUmsParam) {

        MerchantProviderParamsDto merchantProviderParamsDto = new MerchantProviderParamsDto();

        merchantProviderParamsDto.setMerchant_sn(contractSubTask.getMerchant_sn());
        merchantProviderParamsDto.setOut_merchant_sn(contractSubTask.getMerchant_sn());
        merchantProviderParamsDto.setChannel_no(chinaUmsParam.getChannel_no());
        merchantProviderParamsDto.setParent_merchant_id(applyQryResponse.getMerNo());
        merchantProviderParamsDto.setProvider(Integer.valueOf(chinaUmsParam.getProvider()));
        //provider_merchant_id 取appTypeId 为74中的mappno
        String providerMerchantId = null;
        List<ApplyQryResponse.MappInfo> mappInfoList = applyQryResponse.getMappInfoList();
        for (ApplyQryResponse.MappInfo info : mappInfoList) {
            if (UmsAppTypeEnum.PAYMENT_BIZ.getId().equals(info.getApptypeId())) {
                providerMerchantId = info.getMappNo();
            }
        }
        merchantProviderParamsDto.setProvider_merchant_id(providerMerchantId);

        merchantProviderParamsDto.setRule_group_id(contractSubTask.getRule_group_id());
        merchantProviderParamsDto.setContract_rule(contractSubTask.getContract_rule());
        merchantProviderParamsDto.setUpdate_status(1);


        return merchantProviderParamsDto;
    }

    /**
     * 银商 其他支付源  包括微信，支付宝，云闪付
     *
     * @return
     */
    public static MerchantProviderParamsDto umsOtherPayWayToProviderParams(ContractSubTask contractSubTask, ChinaUmsParam chinaUmsParam, Map extra) {
        return otherPayWayToProviderParams(contractSubTask, chinaUmsParam, extra);
    }


    /**
     * 微信重新进件
     *
     * @param providerParam
     * @param contractSubTask
     * @param chinaUmsParam
     * @return
     */
    public static MerchantProviderParamsDto reWechatProviderParams(Map providerParam, ContractSubTask contractSubTask, ChinaUmsParam chinaUmsParam) {

        MerchantProviderParamsDto merchantProviderParamsDto = new MerchantProviderParamsDto();

        merchantProviderParamsDto.setMerchant_sn(contractSubTask.getMerchant_sn());
        merchantProviderParamsDto.setChannel_no(chinaUmsParam.getChannel_no());
        merchantProviderParamsDto.setParent_merchant_id(WosaiMapUtils.getString(providerParam, MerchantProviderParams.PARENT_MERCHANT_ID));
        merchantProviderParamsDto.setProvider(Integer.valueOf(chinaUmsParam.getProvider()));
        merchantProviderParamsDto.setProvider_merchant_id(WosaiMapUtils.getString(providerParam, MerchantProviderParams.PROVIDER_MERCHANT_ID));
        merchantProviderParamsDto.setPayway(contractSubTask.getPayway());
        merchantProviderParamsDto.setRule_group_id(contractSubTask.getRule_group_id());
        merchantProviderParamsDto.setContract_rule(contractSubTask.getContract_rule());
        merchantProviderParamsDto.setExtra(WosaiMapUtils.getMap(providerParam, MerchantProviderParams.EXTRA));
        merchantProviderParamsDto.setUpdate_status(1);


        return merchantProviderParamsDto;

    }

    /**
     *
     *  其他支付源  包括微信，支付宝，云闪付
     *
     * @return
     */
    public static MerchantProviderParamsDto otherPayWayToProviderParams(ContractSubTask contractSubTask, ChannelParam channelParam, Map extra) {
        MerchantProviderParamsDto merchantProviderParamsDto = new MerchantProviderParamsDto();

        merchantProviderParamsDto.setMerchant_sn(contractSubTask.getMerchant_sn());
        merchantProviderParamsDto.setOut_merchant_sn(contractSubTask.getMerchant_sn());
        merchantProviderParamsDto.setChannel_no(channelParam.getChannel_no());
        merchantProviderParamsDto.setProvider(Integer.valueOf(channelParam.getProvider()));
        merchantProviderParamsDto.setPay_merchant_id((String) extra.get(MerchantProviderParams.PAY_MERCHANT_ID));
        merchantProviderParamsDto.setParent_merchant_id((String) extra.get(MerchantProviderParams.PARENT_MERCHANT_ID));
        merchantProviderParamsDto.setProvider_merchant_id((String) extra.get(MerchantProviderParams.PROVIDER_MERCHANT_ID));
        merchantProviderParamsDto.setRule_group_id(contractSubTask.getRule_group_id());
        merchantProviderParamsDto.setContract_rule(contractSubTask.getContract_rule());
        merchantProviderParamsDto.setExtra((Map<String, Object>) extra.get(TRADE));
        merchantProviderParamsDto.setUpdate_status(1);
        return merchantProviderParamsDto;
    }
}
