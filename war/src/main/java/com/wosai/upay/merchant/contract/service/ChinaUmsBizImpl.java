package com.wosai.upay.merchant.contract.service;

import com.alibaba.fastjson.JSONObject;
import com.wosai.upay.merchant.contract.biz.PicBiz;
import com.wosai.upay.merchant.contract.model.chinaums.PicUploadRequestBO;
import com.wosai.upay.merchant.contract.model.chinaums.ChinaUmsResponseWrap;
import com.wosai.upay.merchant.contract.model.chinaums.request.*;
import com.wosai.upay.merchant.contract.model.chinaums.response.*;
import com.wosai.upay.merchant.contract.model.provider.ChinaUmsParam;
import com.wosai.upay.merchant.contract.utils.chinaUms.EncryptAndSign;
import com.wosai.upay.merchant.contract.utils.chinaUms.ChinaUmsResponseConvert;
import com.wosai.upay.merchant.contract.utils.chinaUms.http.ChinaUmsResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;

import java.io.File;
import java.io.FileNotFoundException;
import java.net.MalformedURLException;
import java.util.Map;

/**
 * @Description: 银联商务自助签约
 * <AUTHOR>
 * @Date 2021/2/20 15:51
 */
@Component
@Slf4j
public class ChinaUmsBizImpl implements ChinaUmsBiz {
    /**
     * 自定义业务异常
     */
    private final static  String  BUSINESS_FAIL_RES_CODE = "401";
    private final static  String  BUSINESS_FAIL_RES_MSG = "收钱吧业务异常";

    /**
     * 自定义系统异常
     */
    private final static  String  SYSTEM_FAIL_RES_CODE = "500";
    private final static  String  SYSTEM_FAIL_RES_MSG = "系统异常";

    /**
     * 银联商务自助签约入网
     */
    @Value("${chinaUms.autoReg.url}")
    private String autoRegUrl;

    /**
     * 银联商务子商户配置地址
     */
    @Value("${chinaUms.subDevConf.url}")
    private String subDevConfUrl;

    /**
     * 银联商务子商户配置查询地址
     */
    @Value("${chinaUms.subDevConfQuery.url}")
    private String subDevConfQueryUrl;

    /**
     * 银联商务微信商户进件接口
     */
    @Value("${chinaUms.generalMchntAdd.url}")
    private String generalMchntAddUrl;

    /**
     * 银联商务微信商户进件确认接口
     */
    @Value("${chinaUms.generalMchntAddConfirm.url}")
    private String generalMchntAddConfirmUrl;


    @Autowired
    private PicBiz picBiz;

    @Autowired
    private ChinaUmsResponseConvert chinaUmsResponseConvert;

    @Override
    public ChinaUmsResponseWrap<PicUploadRequestBO, PicUploadResponse> picUpload(PicUploadRequestBO requestBO, ChinaUmsParam param) {
        ChinaUmsBaseResponse response = new PicUploadResponse();
        File file = null;
        final String picUrl = requestBO.getPicUrl();
        log.info("PicUploadRequest req: {}", JSONObject.toJSONString(requestBO));
        try {
            file = picBiz.urlToFile(picUrl, 1);
            byte[] fileByte = FileUtils.readFileToByteArray(file);
            // 加密
            final String encode = Base64Utils.encodeToString(fileByte);
            final String picBase64 = String.format("data:image/%s;base64,%s",file.getName().substring(file.getName().lastIndexOf(".")+1),encode);
            final PicUploadRequest request = PicUploadRequest.builder().requestSeq(requestBO.getRequestSeq()).accesserId(param.getAccesserId()).picBase64(picBase64).build();
            response = ChinaUmsResponseUtil.getHttpResponse(autoRegUrl, request, PicUploadResponse.class,param);
        } catch (FileNotFoundException | MalformedURLException e) {
            log.error("picUpload IOException:{}", e);
            response.setResCode(BUSINESS_FAIL_RES_CODE);
            response.setResMsg(BUSINESS_FAIL_RES_MSG);
        } catch (Exception e) {
            log.error("picUpload Exception:{}", e);
            response.setResCode(SYSTEM_FAIL_RES_CODE);
            response.setResMsg(SYSTEM_FAIL_RES_MSG);
        } finally {//删除临时文件
            FileUtils.deleteQuietly(file);
        }
        return chinaUmsResponseConvert.convert2SqbResponse(requestBO,response);
    }

    @Override
    public ChinaUmsResponseWrap<ComplexUploadRequest, ComplexUploadResponse> complexUpload(ComplexUploadRequest request, ChinaUmsParam param) {
        final ChinaUmsBaseResponse response = ChinaUmsResponseUtil.getHttpResponse(autoRegUrl, request, ComplexUploadResponse.class, param);
        return chinaUmsResponseConvert.convert2SqbResponse(request,response);
    }

    @Override
    public String generateUrl(AgreementSignRequest request, ChinaUmsParam param) {
        final String jsonString = JSONObject.toJSONString(request);
        log.info("generateUrl req: {}", jsonString);
        //json_data明文内容加密成密文
        String key = param.getKey();
        final String jsonData;
        final String signData;
        try {
            jsonData = EncryptAndSign.encrypt(jsonString, key);
            //对json_data明文内容做签名摘要
            signData = EncryptAndSign.sign(jsonString.trim());
            //平台Id
            final String accesserId = param.getAccesserId();
            final StringBuilder builder = new StringBuilder();
           return builder.append(autoRegUrl).append("?json_data=").append(jsonData).append("&sign_data=").append(signData).append("&accesser_id=").append(accesserId).toString();
        } catch (Exception e) {
            log.info("generateUrl Exception:{}", e);
        }
        return null;
    }

    @Override
    public ChinaUmsResponseWrap<ApplyQryRequest, ApplyQryResponse> applyQry(ApplyQryRequest request, ChinaUmsParam param) {
        final ChinaUmsBaseResponse response = ChinaUmsResponseUtil.getHttpResponse(autoRegUrl, request, ApplyQryResponse.class, param);
        return chinaUmsResponseConvert.convert2SqbResponse(request,response);
    }

    @Override
    public ChinaUmsResponseWrap<BranchBankListRequest, BranchBankListResponse> branchBankList(BranchBankListRequest request, ChinaUmsParam param) {
        final ChinaUmsBaseResponse response = ChinaUmsResponseUtil.getHttpResponse(autoRegUrl, request, BranchBankListResponse.class, param);
        return chinaUmsResponseConvert.convert2SqbResponse(request,response);
    }

    @Override
    public ChinaUmsResponseWrap<CompanyAccountVerifyRequest, ChinaUmsBaseResponse> companyAccountVerify(CompanyAccountVerifyRequest request, ChinaUmsParam param) {
        final ChinaUmsBaseResponse response = ChinaUmsResponseUtil.getHttpResponse(autoRegUrl, request, ChinaUmsBaseResponse.class, param);
        return chinaUmsResponseConvert.convert2SqbResponse(request,response);
    }

    @Override
    public ChinaUmsResponseWrap<RequestAccountVerifyRequest, ChinaUmsBaseResponse> requestAccountVerify(RequestAccountVerifyRequest request, ChinaUmsParam param) {
        final ChinaUmsBaseResponse response = ChinaUmsResponseUtil.getHttpResponse(autoRegUrl, request, ChinaUmsBaseResponse.class, param);
        return chinaUmsResponseConvert.convert2SqbResponse(request,response);
    }

    @Override
    public ChinaUmsResponseWrap<SubDevConfAddRequest, SubDevConfAddResponse> subDevConfAdd(SubDevConfAddRequest request, ChinaUmsParam param) {
        final SubDevConfigBaseResponse response = ChinaUmsResponseUtil.getHttpAuthorizationResponse(subDevConfUrl, request, SubDevConfAddResponse.class, param);
        return chinaUmsResponseConvert.convert2SqbResponse(request,response);
    }

    @Override
    public ChinaUmsResponseWrap<SubDevConfQueryRequest, SubDevConfQueryResponse> subDevConfQuery(SubDevConfQueryRequest request, ChinaUmsParam param) {
        final SubDevConfigBaseResponse response = ChinaUmsResponseUtil.getHttpAuthorizationResponse(subDevConfQueryUrl, request, SubDevConfQueryResponse.class, param);
        return chinaUmsResponseConvert.convert2SqbResponse(request,response);
    }

    @Override
    public ChinaUmsResponseWrap<Object, Object> test1(Map request, ChinaUmsParam param) {
        String url = "http://58.247.0.18:29015/v1/netpay/bills/get-qrcode";
        final SubDevConfigBaseResponse response = ChinaUmsResponseUtil.getHttpAuthorizationResponse(url, request, SubDevConfigBaseResponse.class, param);
        return chinaUmsResponseConvert.convert2SqbResponse(request,response);
    }


    @Override
    public ChinaUmsResponseWrap<ComplexAlterAcctInfoRequest, ComplexAlterAcctInfoResponse> complexAlterAcctInfo(ComplexAlterAcctInfoRequest request, ChinaUmsParam param) {
        final ChinaUmsBaseResponse response = ChinaUmsResponseUtil.getHttpResponse(autoRegUrl, request, ComplexAlterAcctInfoResponse.class, param);
        return chinaUmsResponseConvert.convert2SqbResponse(request,response);
    }


    @Override
    public ChinaUmsResponseWrap<AlterSignRequest, AlterSignResponse> alterSign(AlterSignRequest request, ChinaUmsParam param) {
        final ChinaUmsBaseResponse response = ChinaUmsResponseUtil.getHttpResponse(autoRegUrl, request, AlterSignResponse.class, param);
        return chinaUmsResponseConvert.convert2SqbResponse(request,response);
    }

    @Override
    public ChinaUmsResponseWrap<AlterQryRequest, AlterQryResponse> alterQry(AlterQryRequest request, ChinaUmsParam param) {
        final ChinaUmsBaseResponse response = ChinaUmsResponseUtil.getHttpResponse(autoRegUrl, request, AlterQryResponse.class, param);
        return chinaUmsResponseConvert.convert2SqbResponse(request,response);
    }

    @Override
    public ChinaUmsResponseWrap<GeneralMchntAddRequest, GeneralMchntAddResponse> generalMchntAdd(GeneralMchntAddRequest request, ChinaUmsParam param) {
        final GeneralMchntBaseResponse response = ChinaUmsResponseUtil.getHttpAuthorizationResponse(generalMchntAddUrl, request, GeneralMchntAddResponse.class, param);
        return chinaUmsResponseConvert.convert2SqbResponse(request,response);
    }

    @Override
    public ChinaUmsResponseWrap<GeneralMchntAddConfirmRequest, GeneralMchntAddConfirmResponse> generalMchntAddConfirm(GeneralMchntAddConfirmRequest request, ChinaUmsParam param) {
        final GeneralMchntBaseResponse response = ChinaUmsResponseUtil.getHttpAuthorizationResponse(generalMchntAddConfirmUrl, request, GeneralMchntAddConfirmResponse.class, param);
        return chinaUmsResponseConvert.convert2SqbResponse(request,response);
    }
}
