package com.wosai.upay.merchant.contract.model.umb.request;

import com.wosai.upay.merchant.contract.model.annotation.FieldValueSource;
import com.wosai.upay.merchant.contract.model.annotation.IgnoreFieldAutoMapping;
import com.wosai.upay.merchant.contract.model.luzhou.ObjectToJson;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class UMBAddBankCardBody extends UMBBase implements ObjectToJson {
    /**
     * 中投商户号
     */
    @IgnoreFieldAutoMapping
    String merid;
    /**
     *账户号码
     */
    @FieldValueSource(source = "bankAccountBO.number")
    String accountno;
    /**
     * 账户类型. 00对私 01 对公
     */
    @IgnoreFieldAutoMapping
    String accounttype;
    /**
     * 账户名称. 个体：需与经营者姓名一致企业. 小微：需与商户名称一致
     */
    @FieldValueSource(source = "bankAccountBO.holder")
    String accountname;
    /**
     * 对公账户必输
     * 银行名称
     */
    @FieldValueSource(source = "bankAccountBO.bankName")
    String bankname;
    /**
     * 对公账户必输
     * 银行代码.
     * https://docs.msfpay.com/docs/ztkx-cas/ztkx-cas-1el8rolb4e845
     */
    @IgnoreFieldAutoMapping
    String bankcode;

    /**
     * 对公账户必输，不带”省”或”自治区”，如湖南，北京，内蒙古等
     */
    String bankpro;

    /**
     * 对公账户必输，不带”市”字如北京
     */
    @FieldValueSource(source = "bankAccountBO.city")
    String bankcity;

    /**
     * 银行预留手机号.对私四要素的绑卡验证用户上送手机号是否为银行预留手机号
     */
    @FieldValueSource(source = "merchantBO.contactCellphone")
    String prebankmobile;
}
