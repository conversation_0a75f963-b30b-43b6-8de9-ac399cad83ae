product.env=beta
jdbc.driverClassName=com.mysql.jdbc.Driver
jdbc.connection.eviction.interval=60000
jdbc.url=tk-merchant-contract-merchant_contract-4892
#jdbc.url=tk-merchant-contract-job-merchant_contract_dev-7430
#redis
redis.url=r-8vbsiayrjo64l0t62e.redis.zhangbei.rds.aliyuncs.com
redis.port=6379
redis.database=2
redis.password=bRUY^ORbRdNBIR#FgGPn!9Ao32qU
#cacheServiceç¼å­æ°æ®çè¿ææ¶é´ï¼åä½æ¯«ç§
redis.cacheService.expiredTime=86400
#cacheServiceç¼å­æ°æ®keyçåç¼
redis.cacheService.keyPrefix=mini_info

#db config
db.maxActive=5
db.minIdel=2
#core-business
jsonrpc.core-business.server=http://core-business.beta.iwosai.com
jsonrpc.core-crypto.server=http://core-crypto.beta.iwosai.com
#é£æ§ç³»ç»æå¡
jsonrpc.shouqianba-risk.server=http://shouqianba-risk-service
#merchant-audit
jsonrpc.merchant-audit.server=http://merchant-audit-service
jsonrpc.bank-info.server=http://bank-info-service.beta.iwosai.com/
jsonrpc.merchant-business-open=http://merchant-business-open.beta.iwosai.com
#shouqianba-merchant-service
jsonrpc.shouqianba-merchant.server=http://shouqianba-merchant-service
jsonrpc.sales-system.server=http://sales-system-service
#business-log-service
jsonrpc.bizlog.server=http://business-log
jsonrpc.merchant_contract_job.service=http://merchant-contract-job
jsonrpc.merchant_contract_activity.service=http://merchant-contract-activity
jsonrpc.risk-disposal.server=http://risk-disposal
jsonrpc.merchant-center.server=http://merchant-center.beta.iwosai.com
jsonrpc.mini-apps-open.server=http://mini-apps-open.beta.iwosai.com
#æ¥å£ä¿¡æ¯å å¯
jsonrpc.signature-proxy.server=http://signature-proxy
#ocrè¯å«
jsonrpc.shouqianba-tools-service=http://shouqianba-tools-service.beta.iwosai.com
#crmå®¢æ·å³ç³»
jsonrpc.crm-customer-relation=http://crm-customer-relation.beta.iwosai.com
jsonrpc.merchant-user-service.server=http://merchant-user-service
jsonrpc.sales-system-poi.server=http://mock-server.beta.iwosai.com
#kafka facede
message.send.topic=events.upay.merchant-contract-bank-status
message.send.brokers=kafka-beta1.base:9092,kafka-beta2.base:9092,kafka-beta3.base:9092
message.send.registry.url=http://kafka-beta1.base:8081,http://kafka-beta2.base:8081,http://kafka-beta3.base:8081
message.send.batch.size=100
message.send.acks=all
message.send.linger.ms=500
message.send.max.block.ms=3000
message.send.terminal.partitions=10
message.send.enable.idempotence=true

#lkl merchant manage
#lakala config
#çäº§ç¯å¢
lkl.v3.retUrl=https://upay-api.iwosai.com/openplatform/lklV3Callback
#lklV3
lkl.v3.merchant=https://test.wsmsd.cn/sit/api/v2/mms/openApi
lkl.v3.merchant.v2=https://test.wsmsd.cn/sit/api/v2/mms/openApi
lkl.v3.merchant.v3=https://test.wsmsd.cn/sit/api/v3/mms/open_api
lkl.v3.terminal=https://test.wsmsd.cn/sit/api/v3/mms/ws/report/
lkl.v3.query=https://test.wsmsd.cn/sit/api/v3/mms/ws/merch/
lkl.v3.channelno=228783
lkl.v3.cop = https://test.wsmsd.cn/sit/api/v2/mms/cop
lkl.v3.mcqs=https://test.wsmsd.cn/sit/api/v2/mcqs
lkl.v3.report=https://test.wsmsd.cn/sit/api/v3/mms/open_api/report/sync_sub_mer_info
lkl.v3.ecApplyRetUrl=https://pay-apisix.iwosai.com/lkl/ec_apply/push
#é¿éæ°èæµ·
ant.target.serve=openchannel.alipay.com


#å¾®ä¿¡éç½®æ¯ä»ç®å½
weixin.adddevconfig.url=http://mock-server/secapi/mch/addsubdevconfig
weixin.addrecommendconf.url=http://mock-server/secapi/mkt/addrecommendconf
weixin.modifymchinfo.url=http://mock-server/secapi/mch/modifymchinfo
union.weixin.openidqry=https://tpay.95516.com/wx/v1/openid/qry
#é¶è
union.alipay.url=http://mock-server/unionpay/alipay
union.weixin.url=http://mock-server/wx/v1
union.proxy.alipay.url=http://mock-server/unionpay/alipay
union.proxy.weixin.url=http://mock-server/wx/v1
union.weixin.modify.url=http://mock-server/wx/v1/submch/manage/upd
#æµ·ç§
haike.merchant.contract=http://*************:8080
haike.alipay.url=https://sqb-front-test.icardpay.com/ali/aligateway
haike.weixin.url=https://sqb-front-test.icardpay.com/wx/v1
haike.proxy.alipay.url=https://sqb-front-test.icardpay.com/ali/aligateway
haike.proxy.weixin.url=https://sqb-front-test.icardpay.com/wx/v1
fuyou.merchant.contract=http://mock-server.beta.iwosai.com/wmp
fuyou.ftp.url=ftp-1.fuiou.com
fuyou.ftp.port=9021
fuyou.ftp.username=FTP999999
fuyou.ftp.password=8A4GL0qVNd4RWhtK
fuyou.ftp.isUpload=false
fuyou.wildCard.frontReturnUrl=https://device-crm-web.iwosai.com/bank-card/fy-foreign

alipay.merchant.black=http://mock-server/gateway.do
lkl.upload.url=https://nb.lakala.com:8080/UploadAttachment
lkl.upload.version=10
lkl.upload.command=ICP_UPLOAD_ATTACHMENT
lkl.upload.platform=TP
lkl.upload.comOrgCode=247587
spring.application.name=merchant-contract
#nuccbestpay connection config
nuccbestpay.connection.readTimeout=5000
nuccbestpay.connection.connectionTimeout=3000
# è¯·æ³¨æçäº§ç¯å¢éç½®
union_open.ser_provid=S20180919151112EA3E6
union_open.ser_pri_key=MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAKYDOeI5eN0Ehu4f4L36kRgq8jeLLkKiOWMlfSAu5W4EQ+t9GGCzIk6+T8c10p5yMXUt6xGBGYVMY3cxEX+uSjz0HxNGQv9o0olybb8twJvsYghDaQFai70O+0QNL9Xtai8QXcyxkNAQYtkB3jqrIUKWFryfIwG0Qu6W3gmqXDtPAgMBAAECgYAYLGoWVfYSgOFA9WVdZIoSMda5XcvrqgGfPUs+D3YbEgXt2XZeRlddjfrB8EVuCwTGolwTXXAg5+Gn7n400GVAxKfnHrXKAYn8Q1K/wqWgsMRALcn37OrKfakBiD+7RqWYZoLb/GB9RGtT2eXQFj62meW7DiG/icYjp23IO+vM2QJBAOF94bb+bZvvg4YZ+05AKWXgyS0a3Sf0qvqnC48gNnY6NtluQJaPkzR1O3XFK6vUhwIYrqKF4GMao30wGM0GUtMCQQC8eTqcQQk/WnUin2KeTeXyZoCfMa/Ni10SmUrVpAKM63tPZN7iTxHAk5tsBee/1bypXSCG9eEU6jvEd/MExNAVAkEAxtuA5yTR/Sbo0qavHX8wAwyN3h1G1CnNb2dJpls+LF2CaZX1l9Hee/C3E1FjBhJTTeH+gI2YKU4gyBJWjNj6yQJAImgPJw9IXORqldN68WNPIx1LRcSQT3mtmAOcAbxbKtaiQDAt8Cp3FyzbJrE9SWv/cPurQHgKiwLnvkoXdztmsQJAAfjoRIauNMIZTuM5dPO7nsBvETZuVB0sHOVUMJ1mjfo77+czLuVYGrVT9w19NW3XKnDwMpTgOplAwBbExRPqVw==#union_open.host=http://ysf.bcbip.cn/
union_open.host=http://mock-server/
# é¶èäºéªä»è¥éæ´»å¨å°å
#union_open.activity.host=https://partner.95516.com/hulu/
union_open.activity.host=http://putest1.bzqmall.com.cn:8080/hulu/
union_open.isvId=318885770603528192
# å¾®ä¿¡å®åè®¤è¯
weixin.auth.apply=http://mock-server/v3/apply4subject/applyment
weixin.auth.apply.cancel=http://mock-server/v3/apply4subject/applyment/%s/cancel
weixin.auth.apply.query=http://mock-server/v3/apply4subject/applyment?business_code=%s
weixin.auth.query=http://mock-server/v3/apply4subject/applyment/merchants/%s/state
#å¾®ä¿¡b2bè´¹çåæ´
weixin.b2b.feerate=http://mock-server.beta.iwosai.com/retail/B2b/setmchprofitrate

weixin.rate.apply=http://mock-server/v3/rate-activity/applications
weixin.rate.apply.query=http://mock-server/v3/rate-activity/applications/id/%s



# end éèçäº§

weixin.querysubdevconfig.url=http://mock-server/secapi/mch/querysubdevconfig
weixin.querysubmch.url=http://mock-server/secapi/mch/submchmanage?action=query
weixin.mch.audit.info.query=http://mock-server/aipg/Promchrisk/channelquerymchauditinfo

#start tlv2
tlv2.mch=https://syb-test.allinpay.com/vsppcusapi/vsppcusapi/merchantapi/
tlv2.retUrl=https://pay-apisix.iwosai.com/tlv2/callback
tlv2.base.url=https://syb-test.allinpay.com/vsppcusapi/

#end tlv2

#ç¹éè®¡å
weixin.golden.url=http://mock-server/v3/goldplan/merchants/changegoldplanstatus
#åå®¶å°ç¥¨
weixin.ticket.url=http://mock-server/v3/goldplan/merchants/changecustompagestatus
#å¹¿å
weixin.advertise.url=http://mock-server/v3/goldplan/merchants
#é¿éæ°èæµ·
openapi.alipay.gateway=http://mock-server/gateway.do

logback.rootAppender=FT_FILE

#å¾®ä¿¡ç´è¿ç³è¯·
weixin.applyment = http://mock-server.beta.iwosai.com/v3/applyment4sub/applyment
#å¾®ä¿¡ä¸ä¼ å¾ç
weixin.upload.pic = https://api.mch.weixin.qq.com/v3/merchant/media/upload
#å¾®ä¿¡ç´è¿ç¶ææ¥è¯¢
weixin.applyment.query = http://mock-server.beta.iwosai.com/v3/applyment4sub/applyment/business_code/%s
#å¾®ä¿¡ç´B2Bæ´æ°è´¹ç
weixin.b2b.update = http://mock-server.beta.iwosai.com/v3/applyment4sub/applyment/business_code/%s
#æ¯ä»å®ç´è¿
alipay.direct = http://mock-server.beta.iwosai.com/gateway.do

indirect-pay.dev_code=PQPBJGNJC26N
#é¶èåå¡èªå©ç­¾çº¦å¥ç½
chinaUms.autoReg.url=http://mock-server.beta.iwosai.com/self-contract-nmrs/interface/autoReg
#é¶èåå¡å­åæ·éç½®
chinaUms.subDevConf.url=http://mock-server/v1/netpay/wx/sub-dev-conf-add
#é¶èåå¡å­åæ·éç½®
chinaUms.subDevConfQuery.url=http://mock-server/v1/netpay/wx/sub-dev-conf-query
#é¶èåå¡å¾®ä¿¡åæ·è¿ä»¶æ¥å£
chinaUms.generalMchntAdd.url=http://mock-server/v1/netpay/inip/general-mchnt-add
#é¶èåå¡å¾®ä¿¡åæ·è¿ä»¶ç¡®è®¤æ¥å£
chinaUms.generalMchntAddConfirm.url=http://mock-server/v1/netpay/inip/general-mchnt-add-confirm
#é®æ¿å¨èç¸å³å°å
#å¤åæºæåæ·è¿ä»¶
psbc.outMerInfoAdd = http://mock-server.beta.iwosai.com/openplatform/wbMerchant/outMerInfoAdd
#å¤åæºæåæ·ä¿®æ¹
psbc.outMerInfoUpdate = http://mock-server.beta.iwosai.com/openplatform/wbMerchant/outMerInfoUpdate
#å¤åæºæåæ·ç¶æä¿®æ¹
psbc.outMerStaUpdate = http://mock-server.beta.iwosai.com/openplatform/wbMerchant/outMerInfoStatusUpdate
#å¤åæºæåæ·ç¶ææ¥è¯¢
psbc.outMerStaQuery = http://mock-server.beta.iwosai.com/openplatform/wbMerchant/outMerInfoStatusQuery
#å¤åæºæåæ·ä¿¡æ¯ä¸éæ¯ä»æºæ
psbc.outMerSend = http://mock-server.beta.iwosai.com/openplatform/wbMerchant/outMerInfoPayOrg
#å¤åæºæä¸ä¼ å¾ç
psbc.fileUpload = http://mock-server.beta.iwosai.com/openplatform/wbMerchant/merFileUpload
#åæ·è´¦æ·éªè¯
psbc.shopAccountTypeCheck = http://mock-server.beta.iwosai.com/openplatform/wbMerchant/shopAccountTypeCheck
#åæ·è¿ä»¶æ¤éæ¥å£
psbc.outMerInfoAddRevocation = http://mock-server.beta.iwosai.com/openplatform/wbMerchant/outMerInfoAddRevocation
#ééåæ¢
psbc.outMerInfoCutAisle = http://wxtest.smeia.cn/ydsq1/openplatform/wbMerchant/outMerInfoCutAisle
#appidç»å®
psbc.wxAppIdBind=https://103.22.255.138:8055/trans/intermgr/online/api/platform/outAgency/wxAppIdBind
#æ¯ä»ç®å½
psbc.wxPayConf=https://103.22.255.138:8055/trans/intermgr/online/api/platform/outAgency/wxPayConf
#éç½®æ¥è¯¢
psbc.wxConfQue=https://103.22.255.138:8055/trans/intermgr/online/api/platform/outAgency/wxConfQue
#ç»ç«¯æ¥å¤
psbc.terminal.url=https://nap.psbc.com/trans/intermgr/online/api/platform/outAgency/termService/externalPos
#å¾®ä¿¡éç½®å ç­¾å¯ç 
psbc.sm2FilePass=123456
#ææä¿¡æ¯å¬é¥
psbc.sensitive.publicKey=027cc71c023764bd698e5014b9fb94d551891f24cf58ed5e6d80253730f0a63063
#å½å¯ç§é¥
psbc.privateKey=9f23df61e4684889367e4bb9bea624455b5e4845d21a5805ba2854f1af510474
#é®å¨é¨åºç 
psbc.qrCode=https://183.162.222.63:8055/trans/intermgr/online/api/payonline/externalservice
#å¹¿åé¶è¡è¯·æ±å°å
guangfa.url=http://218.13.4.194:30041/gateway/API
#å¹¿åæ¥å¥æ¹ç¼å·ç¼å·
guangfa.accessOrhCd=ORH00001141
#å¹¿åå¾®ä¿¡éç½®
guangfa.wxMchId=1900008721
guangfa.wxChannelId=24006513
#å¹¿åæ¯ä»å®éç½®
guangfa.aliSource=2088501624560335
guangfa.aliOrgPid=2088721382101609
#å¹¿åæ¥å¥ä¸å¡æå¡æ¾ID
guangfa.busMerId=ORH00001141

#å»ºè¡
ccb.dev_code=QV8FBQRYA0FA
ccb.url=http://mock-server.beta.iwosai.com/NCCB/MMER00GatePTReqServlet
ccb.upload.url=http://mock-server.beta.iwosai.com/NCCB/MMER00GatePTReqServlet
#å»ºè¡æ°å­è´§å¸
ccb.decp.url=https://124.127.94.45:8080/NCCB/MMER10GatePTReqServlet
ccb.decp.upload.url=https://124.127.94.45:8080/NCCB/MMER10NEWB2BFilePT
merchant-contract.access_id=50b2a928-55cc-4839-9a70-e33cbd904f23
merchant-contract.access_secret=c9b7607093e249eb8c77632152670172

#åå¤é¶è¡
hx.url=https://paytest.95577.com.cn/mechantApiIn/
hx.callback.url=https://api.iwosai.com/openplatform/hxCallback/merchantContract
hx.dev_code=SGJWMX7JFINP
hx.pathJks=hx/test/kayak.jks
hx.pwdJks=kayakpwd

#å¾®ä¿¡é¨åºç¸å³
wx.store.url=https://api.mch.weixin.qq.com/v3/merchant-store/no-brand-stores
#icbcå·¥åé¶è¡ç§é¥id
icbc.private_key_id=1
icbc.public_key_id=1
#æ±èé¶è¡ç§é¥id
jsb.private_key_id=a3c39d0b-c4e2-42bc-9897-6ef47fbe8464
jsb.public_key_id=edcc290c-4681-45ee-ba1b-ab330701f390
com.wosai.oss.group=cua
com.wosai.oss.imgBucket=private-wosai-images
com.wosai.oss.imgBaseUrl=https://private-images.shouqianba.com
com.wosai.oss.staticBucket=private-wosai-statics
com.wosai.oss.staticBaseUrl=https://private-resource.shouqianba.com
com.wosai.oss.internal=false
#å·¥åé¶è¡
icbc.url=https://gw.open.icbc.com.cn/api/mybank/cardbusiness
#æ±èé¶è¡
jsb.url=https://epaytest.jsbchina.cn:9999/eis/merchant/merchantServices.htm
#èéº»è¡ä¸ä»
fitness.gateway = https://apigw.alipay-eco.com/
#å¹³å®é¶è¡
pab.url=https://************:494
pab.s_signcert_id=*********
pab.sm2_user_id=*********
pab.private_key_id=e0d22883-fa7f-ebac-1c55-d46f6cca6f8a

#æå¡æä¸ä½åå¤å¡
lklForeignCard.devCode = OYLIAHFL3SEK
#ç´è¿devcode
weixin.direct.offline=PJOWY29OWSZI

#æå¡æè¯ä¹¦ç®¡çå¹³å°
lkl.kms.url=https://test.wsmsd.cn/sit/open/api/cert
lkl.kms.key=MBZYP5gj+ON5jmLoTnSeTQ==
lkl.kms.appid=70


pingAn.location=pingan/test/config.properties
pingAn.upload=https://bfiles-stg.pingan.com.cn/brcp/secfile/sec_wefiles_udmp_upload.do
pingAn.h5RequestUrl=https://my-st1.orangebank.com.cn:567/fat4/openapi/gateway/h5/h5/token
#å ä½ç¬¦,ä¸è¦å é¤å¦åæ¥é
pingAn.MockUrl =
pingAn.h5ID=05qfh1ins8nb4zbq

#æ³¸å·é¶è¡
lzb.dev_code=PJNIX0PR4D0I
lzb.host=https://lzshhglpttet.lzccb.cn

guotong.agetAppid=64010200010000000000000000000016

#ä¸­æä¿¡ç§
umb.dev_code=VTP5BF4XDINP
umb_platmer_id_1=umb-CF3000053368
umb_platmer_id_2=umb-CF3000053368
umb.host=http://test.umbpay.com.cn:12080/
umb.callback.url.prefix=https://pay-apisix.iwosai.com/contract/umb/