package com.wosai.upay.merchant.contract.biz;


import com.wosai.upay.merchant.contract.base.BaseTest;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;


public class WeiXinRuleBizTest extends BaseTest {

    @Autowired
    WeiXinRuleBiz weiXinRuleBiz;

    @Test
    public void industryTransfor() {
        String industry = "test";
        Assert.assertEquals(industry, weiXinRuleBiz.industryTransfor(industry, "tonglian"));
        Assert.assertEquals(industry, weiXinRuleBiz.industryTransfor(industry, "lkl"));
        industry="2";
        Assert.assertEquals("756", weiXinRuleBiz.industryTransfor(industry, "lkl"));
        Assert.assertEquals(industry, weiXinRuleBiz.industryTransfor(industry, "tonglian"));

    }

    @Test
    public void getsettleId(){
        Assert.assertEquals("758",weiXinRuleBiz.getSettlementId("0260c061-c759-47c7-ac2d-dddd353f1649", 1, "五金公司"));
    }
}
