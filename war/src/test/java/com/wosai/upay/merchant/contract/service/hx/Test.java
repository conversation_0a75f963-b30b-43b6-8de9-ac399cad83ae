package com.wosai.upay.merchant.contract.service.hx;

import com.sun.org.apache.xerces.internal.impl.dv.util.Base64;

import java.io.FileInputStream;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.cert.Certificate;
import java.util.Enumeration;

/**
 * @Description:
 * <AUTHOR>
 * @Date: 2021/11/10 5:35 下午
 */
public class Test {



    public static void readJks() throws Exception {
        KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
        keyStore.load(new FileInputStream("/Users/<USER>/Desktop/需求开发/华夏银行商户进件/hxmy/prod/shouqianbaprod.jks"), "123456".toCharArray());
        Enumeration<String> aliases = keyStore.aliases();
        String alias = null;
        while (aliases.hasMoreElements()) {
            alias = aliases.nextElement();
        }
        System.out.println("jks文件别名是：" + alias);
        PrivateKey key = (PrivateKey) keyStore.getKey(alias, "123456".toCharArray());
        System.out.println("jks文件中的私钥是：" + new String(Base64.encode(key.getEncoded())));
        Certificate certificate = keyStore.getCertificate(alias);
        PublicKey publicKey = certificate.getPublicKey();
        System.out.println("jks文件中的公钥:" + new String(Base64.encode(publicKey.getEncoded())));
    }


    public static void main(String[] args) throws Exception {
        readJks();

        System.out.println("========");

        System.out.println(CertUtil.getPrivateKey("/Users/<USER>/Desktop/需求开发/华夏银行商户进件/接口文档/测试环境服务商信息/kayak.jks","kayakpwd"));
    }

}
