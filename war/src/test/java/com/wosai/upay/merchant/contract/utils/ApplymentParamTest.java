package com.wosai.upay.merchant.contract.utils;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Map;

/**
 * @Description: 申请单的测试
 * <AUTHOR>
 * @Date 2021/8/20 5:11 下午
 **/
@RunWith(SpringJUnit4ClassRunner.class)     //调用Spring单元测试类
@ContextConfiguration(locations = {"classpath:spring/business-config.xml"})
public class ApplymentParamTest {

    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    @Autowired
//    WeixinApplyUtil weixinApplyUtil;

    @Test
    public void equalTest(){
        String sql = "select * from mch_auth_apply where id = ?";
        MapSqlParameterSource parameters = new MapSqlParameterSource()
                .addValue("id", 142579L);
        Map<String, Object> mchAuthApply = namedParameterJdbcTemplate.queryForMap(sql, parameters);
        System.out.println(mchAuthApply);
    }
}