package com.wosai.upay.merchant.contract.service;

import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.service.MerchantBankService;
import com.wosai.upay.core.service.MerchantBusinessLicenseService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.merchant.contract.biz.ProviderTerminalBiz;
import com.wosai.upay.merchant.contract.constant.LakalaBusinessFileds;
import com.wosai.upay.merchant.contract.constant.LakalaConstant;
import com.wosai.upay.merchant.contract.model.haike.AlipaySyncSpecialFeeRateParam;
import com.wosai.upay.merchant.contract.model.haike.HaikeTerminalRequest;
import com.wosai.upay.merchant.contract.model.provider.HaikeActivityParam;
import com.wosai.upay.merchant.contract.model.provider.HaikeParam;
import com.wosai.upay.merchant.contract.model.weixin.ApplySpecialFeeRateQueryResponse;
import com.wosai.upay.merchant.contract.model.weixin.ApplySpecialFeeRateResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.*;


/**
 * <AUTHOR>
 * @date 2019-10-22
 */
@RunWith(SpringJUnit4ClassRunner.class)     //调用Spring单元测试类
@ContextConfiguration(locations = {"classpath:spring/business-config.xml"})
@Slf4j
public class HaikeServiceTest {

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private MerchantBankService merchantBankService;

    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;

    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    private HaikeService haikeService;

    @Autowired
    ProviderTerminalBiz providerTerminalBiz;


    @Test
    public void contract() {
        String merchantSn = "**************";
        Map contextParam = buildContextParam(merchantSn);
        HaikeParam haikeParam = new HaikeParam();
        haikeParam.setRsaKey("18f059940b244fa88cb03bdd293f7c10");
        haikeParam.setChannel_no("haike");
        haikeParam.setProvider(String.valueOf(ProviderEnum.PROVIDER_HAIKE.getValue()));
        haikeParam.setAgentNo("ISV002571");
        haikeParam.setPayway(0);
        HaikeTerminalRequest request = new HaikeTerminalRequest();
        request.setMerch_no("833F305758140001");
        HaikeTerminalRequest.TerminalInfo terminalInfo = new HaikeTerminalRequest.TerminalInfo();
        List<HaikeTerminalRequest.TerminalInfo> terminalInfoList = new ArrayList<>();
        terminalInfo.setSn("SM00Rvkc");
        terminalInfo.setTerminal_id("SM00Rvkc");
        terminalInfo.setAli_sub_merchant_no("2088720461215811");
        terminalInfoList.add(terminalInfo);
        request.setTerminal_info_list(terminalInfoList);

        haikeService.syncMerchantToHaike("*************");
    }

    private Map buildContextParam(String merchantSn) {
        Map contextParam = new HashMap();
        Map merchant = merchantService.getMerchantByMerchantSn(merchantSn);
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);

        contextParam.put("merchant", merchant);


        ListResult listResult = merchantBankService.findMerchantBankAccountPres(null,
                CollectionUtil.hashMap("merchant_id", merchantId, "default_status", 1));
        if (listResult != null && listResult.getTotal() > 0) {
            Map bankAccount = listResult.getRecords().get(0);
            contextParam.put("bankAccount", bankAccount);
        }

        Map<String, Object> license = merchantBusinessLicenseService.getBusinessLicenseByMerchantId(merchantId);
        contextParam.put("merchantBusinessLicense", license);
        return contextParam;
    }


    @Test
    public void queryRateApplyStatusTest() {
        HaikeParam haikeParam = new HaikeParam();
        haikeParam.setAgentNo("ISV002073");
        haikeParam.setRsaKey("5569e94bd92b4004b084b096ff133ff5");
        try {
            ApplySpecialFeeRateQueryResponse response = haikeService.queryRateApplyStatus("tesss", haikeParam);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void syncAlipayUniversityActivitySuccessResultTest() {
        AlipaySyncSpecialFeeRateParam param = new AlipaySyncSpecialFeeRateParam();
        param.setSubMchId("*********");
        param.setMcc("8220");
        HaikeActivityParam haikeActivityParam = new HaikeActivityParam();
        haikeActivityParam.setAgentApplyNo("***********");
        haikeActivityParam.setMerchNo("833F305754990004");
        haikeActivityParam.setAgentNo("ISV002073");
        haikeActivityParam.setRsaKey("5569e94bd92b4004b084b096ff133ff5");
        haikeActivityParam.setPayway(2);
        haikeActivityParam.setProvider(String.valueOf(ProviderEnum.PROVIDER_HAIKE.getValue()));
        haikeActivityParam.setPayway_channel_no("MC2307181603074oX0");
        haikeActivityParam.setChannel_no("MC2307181603074oX0");

        ApplySpecialFeeRateResponse response = haikeService.syncAlipayUniversityActivitySuccessResult(param, haikeActivityParam);
        Assert.assertEquals("200", response.getCode());
    }


}
