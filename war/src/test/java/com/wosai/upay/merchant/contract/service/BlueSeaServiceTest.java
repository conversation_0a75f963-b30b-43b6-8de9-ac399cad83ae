package com.wosai.upay.merchant.contract.service;

import com.alibaba.fastjson.JSONObject;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.core.service.MerchantBankService;
import com.wosai.upay.core.service.MerchantBusinessLicenseService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.merchant.contract.base.BaseTest;
import com.wosai.upay.merchant.contract.biz.ContractBaseBiz;
import com.wosai.upay.merchant.contract.constant.UnionConstant;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.bluesea.ActivityCreateReq;
import com.wosai.upay.merchant.contract.model.bluesea.CustomizedInfo;
import com.wosai.upay.merchant.contract.model.provider.TongLianParam;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.eq;


public class BlueSeaServiceTest extends BaseTest {

    @SpyBean
    private BlueSeaServiceImpl service;

    /**
     * 内部控件
     */
    @MockBean
    private NewUnionService newUnionService;

    @MockBean
    private ContractBaseBiz contractBaseBiz;

    @MockBean
    private TongLianService tongLianService;

    @MockBean
    private IndustryMapService industryMapService;

    /**
     * 外部控件
     */
    @Mock
    private MerchantService merchantService;

    @Mock
    private MerchantBusinessLicenseService merchantBusinessLicenseService;

    @Mock
    private MerchantBankService merchantBankService;

    @Before
    public void before() {
        ReflectionTestUtils.setField(service, "merchantService", merchantService);
        ReflectionTestUtils.setField(service, "merchantBusinessLicenseService", merchantBusinessLicenseService);
        ReflectionTestUtils.setField(service, "merchantBankService", merchantBankService);
    }

    /**
     * 查询支付宝商户等级
     */
    @Test
    public void queryAlipayMchLevel() throws MpayException, MpayApiNetworkError {
        Map map = CollectionUtil.hashMap("code", "10000");
        //situation normal
        Mockito.doReturn(map).when(newUnionService).queryAlySubMch("mch");
        assertEquals(true, service.queryAlipayMchLevel("mch").isSuccess());

        //situation exception
        Mockito.doThrow(new RuntimeException()).when(newUnionService).queryAlySubMch("mch1");
        assertEquals("支付宝商户信息查询失败", service.queryAlipayMchLevel("mch1").getMessage());
    }

    /**
     * 升级商户等级到M3
     */
    @Test(expected = ContractBizException.class)
    public void updateMerchantToM3() throws MpayException, MpayApiNetworkError {
        //prepare parameter
        CustomizedInfo customizedInfo = new CustomizedInfo();
        customizedInfo.setAddress("address").setAdd_type("add_type").setBusiness("business").setBusiness_license("business_license");
        customizedInfo.setBusiness_license_type("business_license_type").setMerchant_name("merchant_name").setMerchant_shortname("merchant_shortname");
        customizedInfo.setService_phone("service_phone").setCard_no("card_no").setCard_name("card_name").setContact_name("contact_name").setContact_id_no("contact_id_no");
        customizedInfo.setContact_phone("contact_phone").setMcc("mcc");


        Mockito.doReturn("lkl").when(contractBaseBiz).getMerchantAcquirer("lkl");
        Mockito.doReturn("tonglian").when(contractBaseBiz).getMerchantAcquirer("tonglian");

        //situation tonglian 200
        TongLianParam tongLianParam = new TongLianParam();
        tongLianParam.setKey("key").setOrgid("orgid").setPayway(1).setProvider("provider").setChannel_no("channel_no");
        Mockito.doReturn(tongLianParam).when(contractBaseBiz).buildTongLianParam("tonglian-1020-2");
        ContractResponse contractResponse = new ContractResponse();
        contractResponse.setCode(200);
        Mockito.doReturn(contractResponse).when(tongLianService).updateAlipayWithParams(any(), any());

        ListResult listResult = new ListResult();
        ArrayList<Map> listMap = new ArrayList<>();
        HashMap<Object, Object> map1 = new HashMap<>();
        map1.put("id", "value");
        listMap.add(map1);
        listResult.setRecords(listMap);
        listResult.setTotal(1);
        Mockito.doReturn(map1).when(merchantService).getMerchantByMerchantSn(any());
        Mockito.doReturn(listResult).when(merchantBankService).findMerchantBankAccountPres(any(), any());
        Mockito.doReturn(map1).when(merchantBusinessLicenseService).getBusinessLicenseByMerchantId(any());
        assertTrue(service.updateMerchantToM3("tonglian", customizedInfo));
        service.updateMerchantToM3("lkl1", customizedInfo);
    }

    /**
     * 蓝海活动报名
     * 存在内部new对象调用外部HTTP，暂时无法mockquanbu mock
     */
    @Test(expected = ContractBizException.class)
    public void activityCreate() {
        ActivityCreateReq req = new ActivityCreateReq();
        req.setBusiness_license_pic("business_license_pic").setCheckstand_pic("checkstand_pic").setIndoor_pic("indoor_pic").setSettled_pic("settled_pic");
        req.setShop_entrance_pic("shop_entrace_pic").setActivity_type("activity_type").setAlias_name("alias_name").setName("name").setSub_merchant_id("sub_merchant_id");

//        AntMerchantExpandIndirectImageUploadResponse response = new AntMerchantExpandIndirectImageUploadResponse();
//        response.setBody("body");
//        response.setImageId("imageId");

        Mockito.doReturn("lkl").when(contractBaseBiz).getMerchantAcquirer("lkl");
        Mockito.doReturn("tonglian").when(contractBaseBiz).getMerchantAcquirer("tonglian");
        Mockito.doReturn(null).when(contractBaseBiz).buildTongLianParam("tonglian-1020-2");
        Mockito.doReturn(null).when(tongLianService).activityCreate(any(), any(), any());
        assertNull(service.activityCreate("tonglian", "url", req));

        service.activityCreate("lkl1", "url", req);
    }

    /**
     * 蓝海活动确认
     */
    @Test(expected = ContractBizException.class)
    public void activityConfirm() {
        Mockito.doReturn("lkl").when(contractBaseBiz).getMerchantAcquirer("lkl");
        Mockito.doReturn("tonglian").when(contractBaseBiz).getMerchantAcquirer("tonglian");
        Mockito.doReturn(null).when(contractBaseBiz).buildTongLianParam("tonglian-1020-2");
        Mockito.doReturn(null).when(tongLianService).activityCreate(any(), any(), any());
        assertNull(service.activityConfirm("tonglian", "url"));

        service.activityConfirm("lkl1", "url");
    }


    /**
     * @Description:只限于更新 *********** 渠道 拉卡拉万马 支付宝升级 商户至M3
     **/

    @Test(expected = ContractBizException.class)
    public void updateMerchantToM3V2() throws Exception {
        //prepare parameter
//        Map map = CollectionUtil.hashMap("id", "id", "name", "name", "service_phone", "service_phone", "industry", "industry", "street_address", "street_address");
//        Map license = CollectionUtil.hashMap(MerchantBusinessLicence.NUMBER, "number");
//
//        ListResult listResult = new ListResult();
//        ArrayList<Map> listMap = new ArrayList<>();
//        Map bankAccout = CollectionUtil.hashMap("holder", "holder", "identity", "identity", "type", 2, "number", "number");
//        listMap.add(bankAccout);
//        listResult.setRecords(listMap);
//        listResult.setTotal(1);
//
//
//        Mockito.doReturn(map).when(merchantService).getMerchantByMerchantSn(any());
//        Mockito.doReturn(listResult).when(merchantBankService).findMerchantBankAccountPres(any(), any());
//        Mockito.doReturn(license).when(merchantBusinessLicenseService).getBusinessLicenseByMerchantId("id");
//        Mockito.doReturn("lkl").when(contractBaseBiz).getMerchantAcquirer("lkl");
//        Mockito.doReturn("tonglian").when(contractBaseBiz).getMerchantAcquirer("tonglian");
//        Mockito.doReturn("industryAlyCode").when(industryMapService).getIndustryCode(any(), eq("1"), eq(UnionConstant.WM_ALY));
//        Mockito.doReturn("unionMcc").when(industryMapService).getIndustryCode(any(), eq("1"), eq(UnionConstant.WM_ALY_MCC));
//
//        license.put(MerchantBusinessLicence.LEGAL_PERSON_NAME, "legal_person_name");
//        license.put(MerchantBusinessLicence.LEGAL_PERSON_ID_NUMBER, "legal_person_id_number");
//        assertEquals(new HashMap<>(), service.updateMerchantToM3("lkl"));
//
//        TongLianParam tongLianParam = new TongLianParam();
//        tongLianParam.setOrgid("orgid").setKey("key").setPayway(1).setProvider("provider").setChannel_no("channel_no");
//        ContractResponse response = new ContractResponse();
//        response.setCode(200);
//        Mockito.doReturn(tongLianParam).when(contractBaseBiz).buildTongLianParam("tonglian-1020-2");
//        Mockito.doReturn(response).when(tongLianService).updateAlipayWithParams(any(),any());
//        assertEquals(1, service.updateMerchantToM3("tonglian").get("result_code"));
//
//        response.setCode(201);
//        Mockito.doReturn(response).when(tongLianService).updateAlipayWithParams(any(),any());
//        assertEquals(0, service.updateMerchantToM3("tonglian").get("result_code"));

        service.updateMerchantToM3V2("2088020316971432","1680005795401", JSONObject.toJavaObject(JSONObject.parseObject("{\"merchant_name\":\"山东中膳信合餐饮管理有限公司\",\"merchant_shortname\":\"奥尔良烤鸡\",\"service_phone\":\"***********\",\"business\":\"2015050700000000\",\"business_license\":\"91370213MABYDR3P63\",\"business_license_type\":\"NATIONAL_LEGAL_MERGE\",\"address\":\"九水东路号\",\"add_type\":\"BUSINESS_ADDRESS\",\"card_no\":\"6217002200004537690\",\"card_name\":\"张久龙\",\"contact_name\":\"张久龙\",\"contact_id_no\":\"320721199211175018\",\"contact_phone\":\"***********\",\"mcc\":\"5880\",\"forceUpdate\":true,\"forceUpdateValue\":{\"name\":\"山东中膳信合餐饮管理有限公司\",\"mcc\":\"5880\"}}"),CustomizedInfo.class));
    }
}
