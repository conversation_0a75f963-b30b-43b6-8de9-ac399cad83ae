package com.wosai.upay.merchant.contract.converter;

import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.core.service.MerchantBankService;
import com.wosai.upay.core.service.MerchantBusinessLicenseService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.merchant.contract.biz.ProviderTerminalBiz;
import com.wosai.upay.merchant.contract.model.haike.AlipaySyncSpecialFeeRateParam;
import com.wosai.upay.merchant.contract.model.haike.HaikeTerminalRequest;
import com.wosai.upay.merchant.contract.model.provider.HaikeActivityParam;
import com.wosai.upay.merchant.contract.model.provider.HaikeParam;
import com.wosai.upay.merchant.contract.model.weixin.ApplySpecialFeeRateQueryResponse;
import com.wosai.upay.merchant.contract.model.weixin.ApplySpecialFeeRateResponse;
import com.wosai.upay.merchant.contract.service.HaikeService;
import com.wosai.upay.merchant.contract.utils.OSSBase64Converter;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2019-10-22
 */
@RunWith(SpringJUnit4ClassRunner.class)     //调用Spring单元测试类
@ContextConfiguration(locations = {"classpath:spring/business-config.xml"})
@Slf4j
public class OssBase64ConverterTest {

    @Resource
    private OSSBase64Converter ossBase64Converter;

    @Test
    public void testUrlToBase64() {
        String testUrl = "https://images.wosaimg.com/f6/56e455967a7e2ec754e4cdcccac358e5214b81.pdf";
        String testBigFileUrl = "https://images.wosaimg.com/f6/56e455967a7e2ec754e4cdcccac358e5214b81.pdf";
        String outputDir = "/Users/<USER>/Downloads/temp";
        try {
            log.info("开始转换OSS文件为Base64编码: {}", testUrl);
            String base64String = ossBase64Converter.urlToBase64(testUrl);

            // 验证Base64编码不为空
            Assert.assertNotNull("Base64编码不应为空", base64String);
            Assert.assertTrue("Base64编码长度应大于0", base64String.length() > 0);
            log.info("Base64编码转换成功，长度: {}", base64String.length());

            // 创建输出目录
            File outputDirectory = new File(outputDir);
            if (!outputDirectory.exists()) {
                boolean created = outputDirectory.mkdirs();
                if (!created) {
                    throw new RuntimeException("无法创建输出目录: " + outputDir);
                }
                log.info("创建输出目录: {}", outputDir);
            }

            // 从URL中提取文件名和扩展名
            String fileName = extractFileNameFromUrl(testUrl);
            if (fileName == null || fileName.isEmpty()) {
                fileName = "downloaded_file_" + System.currentTimeMillis();
            }

            // 确保文件名包含扩展名
            if (!fileName.contains(".")) {
                fileName += ".pdf"; // 默认为PDF扩展名
            }

            // 生成完整的文件路径
            String outputFilePath = outputDir + File.separator + fileName;
            File outputFile = new File(outputFilePath);

            // 将Base64编码解码并写入文件
            byte[] decodedBytes = Base64.getDecoder().decode(base64String);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                fos.write(decodedBytes);
                fos.flush();
            }

            Assert.assertTrue("文件应该存在", outputFile.exists());
            Assert.assertTrue("文件大小应该大于0", outputFile.length() > 0);

            log.info("文件成功保存到: {}", outputFilePath);
            log.info("文件大小: {} bytes", outputFile.length());
            log.info("=== 测试结果 ===");
            log.info("原始URL: " + testUrl);
            log.info("Base64编码长度: " + base64String.length());
            log.info("保存文件路径: " + outputFilePath);
            log.info("文件大小: " + outputFile.length() + " bytes");
            log.info("测试完成！");

        } catch (Exception e) {
            log.error("测试失败", e);
            Assert.fail("测试过程中发生异常: " + e.getMessage());
        }
    }

    /**
     * 从URL中提取文件名
     *
     * @param url 文件URL
     * @return 文件名
     */
    private String extractFileNameFromUrl(String url) {
        if (url == null || url.isEmpty()) {
            return null;
        }

        try {
            String cleanUrl = url.split("\\?")[0];
            String[] parts = cleanUrl.split("/");
            if (parts.length > 0) {
                return parts[parts.length - 1];
            }
        } catch (Exception e) {
            log.warn("无法从URL提取文件名: {}", url, e);
        }

        return null;
    }


}
