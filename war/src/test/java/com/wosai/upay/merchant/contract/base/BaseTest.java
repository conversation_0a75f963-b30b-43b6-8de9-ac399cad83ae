package com.wosai.upay.merchant.contract.base;

//@RunWith(PowerMockRunner.class)
//@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
//@PowerMockIgnore({"javax.management.*", "javax.crypto.*", "javax.net.ssl.*"})
//@ContextConfiguration(locations = {"classpath:spring/business-config.xml"})
//@WebAppConfiguration
//@Ignore

import org.junit.Ignore;
import org.junit.Rule;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

@ContextConfiguration(locations = {"classpath:spring/business-config.xml"})
//@RunWith(SpringJUnit4ClassRunner.class)
@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@PowerMockIgnore({"javax.management.*", "javax.crypto.*", "javax.net.ssl.*"})
@WebAppConfiguration
@Ignore
public class BaseTest {

    @Rule
    public ExpectedException thrown = ExpectedException.none();
}

