<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.wosai.upay</groupId>
        <version>2.62.2</version>
        <artifactId>merchant-contract-parent</artifactId>
    </parent>

    <artifactId>merchant-contract-api</artifactId>
    <version>2.62.2</version>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>3.8.1</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>jsonrpc4j</artifactId>
            <version>2.2.4-alpha</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>upay-common</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>core-business-api</artifactId>
            <version>${core-business.version}</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.nextgen</groupId>
            <artifactId>data-common</artifactId>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.springframework/spring-test -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>4.3.4.RELEASE</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>merchant-contract-job-api</artifactId>
            <version>4.13.6</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.middleware</groupId>
                    <artifactId>logging-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>net.logstash.logback</groupId>
                    <artifactId>logstash-logback-encoder</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.upay</groupId>
                    <artifactId>merchant-contract-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.shouqianba</groupId>
                    <artifactId>cua-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>merchant-contract-activity-api</artifactId>
            <version>1.3.24</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wosai.upay</groupId>
                    <artifactId>merchant-contract-job-api</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>merchant-contract-api</artifactId>
                    <groupId>com.wosai.upay</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.4</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-sdk-java</artifactId>
            <version>4.33.12.ALL</version>
            <exclusions>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcprov-jdk15on</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai.mc</groupId>
            <artifactId>merchant-center-api</artifactId>
            <version>1.6.2</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 其他依赖 -->
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
            <version>1.4</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.8.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
    </dependencies>
</project>
