package com.wosai.upay.merchant.contract.model.newBlueSea.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2020/12/15 14:30
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Accessors(chain = true)
@Builder
public class AntMerchantExpandOrderQueryReq implements Serializable {
    /**
     * 申请单Id
     */
    @NotBlank(message = "申请单Id不为空")
    @JSONField(name = "order_id")
    @Length(max = 64)
    private String orderId;
}
