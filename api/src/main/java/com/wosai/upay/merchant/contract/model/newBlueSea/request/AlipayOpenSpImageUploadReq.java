package com.wosai.upay.merchant.contract.model.newBlueSea.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.NotBlank;


/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2020/11/24 11:49
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Accessors(chain = true)
@Builder
public class AlipayOpenSpImageUploadReq {

    /**
     * 文件url
     */
    @NotBlank(message = "图片地址必填")
    private String url;

}
