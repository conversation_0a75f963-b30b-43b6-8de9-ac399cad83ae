package com.wosai.upay.merchant.contract.model.guangfa.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

import static com.wosai.upay.merchant.contract.constant.guangfa.GuangFaConstant.NO;

/**
 * @Description:
 * <AUTHOR>
 * @Date: 2021/6/3 5:57 下午
 */
@Data
public class GFIprsInfo {
    /**
     * 监控类型
     * 1-地址上送
     * 2-禁止交易
     */
    @NotBlank(message = "监控类型不能为空")
    @Length(max = 1)
    private String monitorTp;

    /**
     * 监控范围
     * 0-省级
     * 1-市级
     * 2-区县
     */
    @NotBlank(message = "监控范围不能为空")
    @Length(max = 1)
    private String monitorRange;

    // TODO: 2021/6/3 代理机构
    /**
     * 代理机构号
     */
    @NotBlank(message = "代理机构号不能为空")
    @Length(max = 16)
    private String agencyOrgId;
    /**
     * 代理机构名称
     */
    @NotBlank(message = "代理机构名称不能为空")
    @Length(max = 256)
    private String agencyOrgNm;

    /**
     * 是否允许贷记卡交易 Y-是 N-否
     */
    @NotBlank(message = "是否允许贷记卡交易不能为空")
    @Length(max = 1)
    @JSONField(name = "isAllowCredit")
    private String allowCredit;

    /**
     * 微信收单机构商户号
     */
    @Length(max = 32)
    private String wxMchId;
    /**
     * 微信收单机构为其渠道商申请
     */
    @Length(max = 32)
    private String wxChannelId;

    /**
     * 与银行或机构合作的伙伴标识，填写该合作伙伴在支付宝的pid
     */
    @Length(max = 32)
    private String aliSource;
    /**
     * 收单机构(例如银行）的标识
     */
    @Length(max = 32)
    private String aliOrgPid;
    /**
     * 卡类型
     * 0000000000000000 0-未开通;1-开通 第一位 本行借记卡 第二位 本行信用卡 第三位 他行借记卡 第四位 他行信用卡 后续预留
     */
    @Length(max = 16)
    private String supCardTp = "1000000000000000";

    /**
     * 经营场所名
     */
    @Length(max = 256)
    private String busAddrNm;

    /**
     * 是否开通免密免签
     */
    @Length(max = 1)
    @JSONField(name = "isNotAuth")
    private String notAuth;

    /**
     * 是否申请优惠价格
     */
    @Length(max = 1)
    private String applyForDisc = NO;
    /**
     * 商户现场注册标识码
     */
    @Length(max = 32)
    private String unionRegId;

    /**
     * 收单机构
     */
    @Length(max = 10)
    private String acqBrh;
    /**
     * 收单机构名称
     */
    @Length(max = 128)
    private String acqBrhNm;

    /**
     * 支付宝经营类目
     */
    @Length(max = 20)
    private String aliCategoryId;

    /**
     * 支付宝账号
     */
    @Length(max = 128)
    private String aliLogonId;
    /**
     * 刷卡交易权限
     * 0000000000000000 0-不支持;1-支持 第一位：消费 第二位：查询 第三位：消费撤销 第四位：退货
     */
    @Length(max = 16)
    private String transAuth;

    /**
     * 签约类型
     * onlineDigit-电子合同签约 offlineCommon-纸质合同签约
     */
    @NotBlank(message = "签约类型不能为空")
    @Length(max = 20)
    private String contraceSignType;
    /**
     * 商户是否贴息
     */
    private String mchntPayFee;

    /**
     * 申请设备终端类型 contraceSignType=onlineDigit
     * 0000000 第1位-传统固定POS 第2位-传统移动POS 第3位-智能POS 第4位-二维码 第5位-公众号 第6位-刷脸机器 第7位-互联网软件类
     */
    private String applyTerminalType;
    /**
     * 申请终端台数 contraceSignType=onlineDigit
     */
    private String applyTerminalNum;
    /**
     * 终端费用模式 contraceSignType=onlineDigit
     * 00第1位:押金 第2位:租金
     */
    private String terminalMode;
    /**
     * 押金 contraceSignType=on lineDigit
     */
    private String cashPledge;
    /**
     * 租金 contraceSignType=on lineDigit
     */
    private String rent;

}
