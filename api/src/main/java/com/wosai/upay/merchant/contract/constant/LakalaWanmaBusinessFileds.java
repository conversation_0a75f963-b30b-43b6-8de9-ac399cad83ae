package com.wosai.upay.merchant.contract.constant;

import java.util.Arrays;
import java.util.List;

/**
 * Created by z<PERSON><PERSON><PERSON> on 17/6/23.
 */
public class LakalaWanmaBusinessFileds {
    public static final String RECE_ORG_NO = "rece_org_no";				// 受理机构编号
    public static final String TECH_ORG_NO = "tech_org_no";				// 技术服务方
    public static final String TECH_ORG_KEY = "tech_org_key";			// 技术服务方密钥
    public static final String VERNO = "verno";							// 版本号
    public static final String RECE_MERCHANT_ID = "rece_merchant_id";	// 受理机构端商户号
    public static final String OUT_MERCHANT_ID = "out_merchant_id";		// 外部商户编号
    public static final String MERCHANT_NAME = "merchant_name";			// 商户名称
    public static final String MERCHANT_SHORTNAME = "merchant_shortname";	// 商户简称
    public static final String SERVICE_PHONE = "service_phone"; 		// 客服电话
    public static final String BUSINESS = "business";					// 经营类目
    public static final String CONTACT_NAME = "contact_name";			// 联系人
    public static final String CONTACT_PHONE = "contact_phone";			// 联系电话
    public static final String CONTACT_EMAIL = "contact_email";			// 联系邮箱
    public static final String NONCE_STR = "nonce_str";					// 随机字符串
    
    // 微信进件个性化信息
    public static final String CHANNEL_ID = "channel_id";				// 渠道号
    public static final String CONTACT_WECHATID_TYPE = "contact_wechatid_type";	// 联系人微信帐号
    public static final String CONTACT_WECHATID = "contact_wechatid";	// 商户备注
    public static final String MERCHANT_REMARK = "merchant_remark";		// 随机字符串
    public static final String SIGN_TYPE = "sign_type";					// 签名类型
    public static final String SIGN = "sign";							// 签名
    
    // 支付宝进件个性化信息
    public static final String SOURCE = "source"; 						// 来源机构标志
    public static final String BUSINESS_LICENSE = "business_license";	// 商户证件编码
    public static final String BUSINESS_LICENSE_TYPE = "business_license_type";	// 商户证件类型
    public static final String CONTACT_ID_NO = "contact_id_no";			// 身份证号
    public static final String PROVINCE_CODE = "province_code";			// 商户所在省
    public static final String CITY_CODE = "city_code";					// 商户所在市
    public static final String DISTRICT_CODE = "district_code";			// 商户所在区县
    public static final String ADDRESS = "address";						// 商户详细地址
    public static final String LONGITUDE = "longitude";					// 经度
    public static final String LATITUDE = "latitude";					// 纬度
    public static final String ADD_TYPE = "add_type";					// 地址类型
    public static final String CARD_NO = "card_no";						// 银行卡号
    public static final String CARD_NAME = "card_name";					// 持卡人姓名

    public static final String MCC = "mcc";						// 商户类别码


    public static final String RETURN_CODE = "return_code";				// 返回状态码
    public static final String RETURN_MSG = "return_msg";				// 返回信息
    public static final String MERCHANT_ID = "merchant_id";				// 商户号
    public static final String SUB_MERCHANT_ID = "sub_merchant_id";		// 微信商户号
    public static final String RESULT_CODE = "result_code";				// 返回状态码
    public static final String ERR_CODE = "err_code";                   //返回错误码
    public static final String ERR_CODE_DES = "err_code_des";				// 返回错误信息


    public static final String MILLIONBEZRA_ID = "millionbezraId";		// 万码系统子商户号
    
    public static final String SUBSCRIBE_APPID = "subscribe_appid";		// 商户推荐关注公众账号APPID
    public static final String WEIXIN_RECEIPT_APPID = "receipt_appid";
    public static final String SUB_APPID = "sub_appid";					// 商户 SubAPPID
    public static final String SUB_APP_SUBJECT = "sub_app_subject";		// 商户微信公众号账号主体
    public static final String JSAPI_PATH = "jsapi_path";				// 商户公众账号JSAPI支付授权目录
    public static final String JSAPI_PATH_LIST = "jsapi_path_list";		// 商户公众账号JSAPI支付授权目录
    public static final String APPID_CONFIG_LIST = "appid_config_list"; // 商户 APPID配置 列表
    public static final String WEIXIN_SUB_APP_SUBJECT = "weixin_sub_app_subject";		// 商户微信公众号账号主体
    
    public static final String REQ_DATE = "req_date"; 					// 请求日期
    public static final String REQ_TIME = "req_time"; 					// 请求时间
    public static final String FILE_TYPE = "file_type"; 				// 文件类型
    public static final String FILE_NAME = "file_name"; 				// 文件名
    public static final String BACK_URL = "back_url"; 					// 结果文件通知地址

    /**
     * merchant_name ：绿洲营业执照名称（商户名）
     merchant_shortname ：商户经营名称
     source：****************（固定）
     service_phone：***********（固定）
     business_license：营业执照注册号
     business_license_type：NATIONAL_LEGAL
     address_info：商户所在城市和详细地址 （详细地址poi偏移后再传给支付宝）
     add_type ：BUSINESS_ADDRESS
     bankcard_info：card_no ：结算账户银行卡号、card_name 姓名
     business ：****************
     contact_name ：开户人姓名、
     contact_id_no ：结算账户开户人证件号、
     contact_phone ：固定***********；
     mcc: 商户类别码(银联支付宝新增字段)
     */
    public static final List<String>  M3_COLUMNS = Arrays.asList(MERCHANT_NAME, MERCHANT_SHORTNAME, SERVICE_PHONE, BUSINESS_LICENSE,
            BUSINESS_LICENSE_TYPE, ADDRESS,ADD_TYPE, CARD_NAME, CARD_NO, BUSINESS, CONTACT_NAME, CONTACT_ID_NO, CONTACT_PHONE, MCC);
    
}
