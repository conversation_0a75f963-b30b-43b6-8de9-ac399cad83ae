package com.wosai.upay.merchant.contract.enume;

/**
 * 身份证正面: ID_CARD_FRONT
 * 身份证反面: ID_CARD_BEHIND
 * 银行卡: BANK_CARD
 * 营业执照: BUSINESS_LICENCE
 * 合影照片: PERSONAL_PHOTO
 * 商户照片: MERCHANT_PHOTO
 * 其他: OTHERS
 * <p>
 * Created by hzq on 17/11/3.
 */
public enum LklPicType {
    ID_CARD_FRONT,
    ID_CARD_BEHIND,
    BANK_CARD,
    BUSINESS_LICENCE,
    PERSONAL_PHOTO,
    MERCHANT_PHOTO,
    OTHERS;

    public static LklPicType toPic(String value) {
        for (LklPicType status : LklPicType.values()) {
            if (status.toString().equalsIgnoreCase(value)) {
                return status;
            }
        }
        return LklPicType.OTHERS;
    }
}
