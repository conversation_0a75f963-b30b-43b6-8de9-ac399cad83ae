package com.wosai.upay.merchant.contract.model.psbc.request;

import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

/**
 * @Description: 外包机构商户状态修改请求参数
 * <AUTHOR>
 * @Date 2021/4/9 09:36
 */
@Data
@SuperBuilder
@Accessors(chain = true)
public class OutMerInfoStatusUpdateRequest extends PsbcBaseRequest {
    /**
     * 交易码
     */
    private String reqCode;

    /**
     * 收单商户号 收单系统分配的商户号
     */
    @NotBlank(message = "收单商户号 不能为空")
    @Length(max = 15)
    private String merId;

    /**
     * 外包商户号
     */
    @NotBlank(message = "外包商户号 不能为空")
    @Length(max = 15)
    private String outMerId;

    /**
     * 状态修改操作
     * 01-启用
     * 02-停用
     * 03-注销
     */
    @NotBlank(message = "状态 不能为空")
    @Length(max = 2)
    private String staUpdate;


    /**
     * 操作员号
     */
    @NotBlank(message = "操作员号 不能为空")
    @Length(max = 11)
    private String oprId;
}
