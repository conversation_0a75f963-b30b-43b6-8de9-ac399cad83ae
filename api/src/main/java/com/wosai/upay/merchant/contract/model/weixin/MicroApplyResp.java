package com.wosai.upay.merchant.contract.model.weixin;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author: jerry
 * @date: 2019/8/26 11:23
 * @Description:
 */
@Data
@Accessors(chain = true)
public class MicroApplyResp extends MicroResp {

    private String applyment_id;
    private String applyment_state;
    private String applyment_state_desc;
    private String sub_mch_id;
    private String sign_url;
    private String audit_detail;


}
