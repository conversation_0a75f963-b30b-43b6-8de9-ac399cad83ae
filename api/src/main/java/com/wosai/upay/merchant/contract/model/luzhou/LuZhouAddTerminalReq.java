package com.wosai.upay.merchant.contract.model.luzhou;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 渠道合作商通过该接口，将条码支付受理终端/条码辅助受理终端前往支付宝/微信/银联报备设备。
 * 若报备的机具在收单系统不存在，该功能还会将该终端入库。
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LuZhouAddTerminalReq extends LuzhouBasicReq implements ObjectToJson {
    /**
     * 机具序列号
     */
    private String deviceSn;
    /**
     * 终端类型.默认为BOX
     * SCAN_POS 扫码POS
     * BOX 扫码盒子
     * GUN 扫码枪
     */
    private String deviceType = "BOX";
    /**
     * 绑定商户号
     */
    private String merchantNo;
    /**
     * 绑定门店号
     */
    private String storeNo;
}
