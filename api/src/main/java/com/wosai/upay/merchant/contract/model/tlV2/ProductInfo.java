package com.wosai.upay.merchant.contract.model.tlV2;

import com.wosai.upay.merchant.contract.model.tlV2.enume.FeeCycleEnum;
import com.wosai.upay.merchant.contract.model.tlV2.enume.MtrxCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductInfo {
    /**
     * 产品名称
     * 最大长度: 8
     * P0003-网上收银
     * 不可为空
     */
    private String pid;

    /**
     * 交易类型
     * 最大长度: 20
     * 详见附录交易类型
     * 不可为空
     *
     * @see MtrxCodeEnum
     */
    private String mtrxcode;

    /**
     * 费率
     * 最大长度: 8
     * 千分之,例如3.5代表千分之3.5
     * 不可为空
     */
    private String feerate;

    /**
     * 信用卡费率
     * 最大长度: 8
     * 千分之,例如3.5代表千分之3.5
     * 借贷分离者必填
     */
    private String creditrate;

    /**
     * 收费周期
     * 最大长度: 2
     * 不可为空
     *
     * @see FeeCycleEnum
     */
    private String feecycle;

    /**
     * 保底金额
     * 单位: 元
     * 例如保底10元填：10
     */
    private String lowlimit;

    /**
     * 封顶金额
     * 单位: 元
     * 例如封顶100元填：100
     */
    private String toplimit;

    /**
     * 费率数组
     * 最大长度: 2000
     * 费率数组,具体模式看feeratetype
     */
    private List<RankRate> rankrate;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RankRate {
        /**
         * 借记卡费率
         * 最大长度: 8
         * 为千分比,例如3.5代表千分之3.5
         */
        private String feerate;

        /**
         * 贷记卡费率
         * 最大长度: 8
         * 为千分比,例如3.5代表千分之3.5
         */
        private String creditrate;

        /**
         * 借记卡保底值
         * 单位: 元
         * 例如保底10元填：10
         */
        private String lowlimit;

        /**
         * 借记卡封顶值
         * 单位: 元
         * 例如封顶100元填：100
         */
        private String toplimit;

        /**
         * 区间
         * 最大长度: 8
         * 格式: 0-1000
         */
        private String rank;
    }
}