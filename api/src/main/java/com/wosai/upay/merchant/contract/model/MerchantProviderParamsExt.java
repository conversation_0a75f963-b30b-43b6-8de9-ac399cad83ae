package com.wosai.upay.merchant.contract.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Created by hzq on 19/11/22.
 */
@Data
@Accessors(chain = true)
public class MerchantProviderParamsExt {

    public Long id;
    public String param_id;
    public Integer type;
    public String ext_field_1;
    public String ext_field_2;
    public String extra;
    public String create_at;
    public String update_at;

    public static final String PARAM_ID = "param_id";
    public static final String TYPE = "type";
    public static final String EXT_FIELD_1 = "ext_field_1";
    public static final String EXT_FIELD_2 = "ext_field_2";
    public static final String CREATE_AT = "create_at";
    public static final String UPDATE_AT = "update_at";

    public static final String EXTRA = "extra";
    public static final Integer TYPE_ZHIMA_MCH = 1; //芝麻入住信息   ext_field_1 保存芝麻入住的serviceId ext_field_2保存芝麻入住version

}
