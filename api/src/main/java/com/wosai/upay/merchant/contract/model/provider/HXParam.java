package com.wosai.upay.merchant.contract.model.provider;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * @Description:
 * <AUTHOR>
 * @Date: 2021/11/1 9:30 上午
 */
@Getter
@Setter
public class HXParam extends ChannelParam {

    /**
     * 收钱吧生成的私钥
     */
    @NotEmpty(message = "收钱吧私钥不能为空")
    private String privateKey;

    /**
     * 华夏公钥
     */
    @NotEmpty(message = "华夏公钥不能为空")
    private String hxPublicKey;
    /**
     * appId
     */
//    private String appId;
}
