package com.wosai.upay.merchant.contract.model.zft;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2023/7/27 下午2:38
 */
@Data
public class IndirectZftOrderQueryDTO {
    /**
     * 申请单id。通过 ant.merchant.expand.indirect.zft.create(直付通二级商户创建)接口返回。与 external_id 二选一必填，若同时传入将以 order_id 为准进行查询。
     */
    @JSONField(name = "order_id")
    private String orderId;
    /**
     * 进件申请时的外部商户id，与order_id二选一必填
     */
    @JSONField(name = "external_id")
    private String externalId;

}
