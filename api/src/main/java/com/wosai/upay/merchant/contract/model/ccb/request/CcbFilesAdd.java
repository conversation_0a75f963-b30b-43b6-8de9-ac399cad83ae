package com.wosai.upay.merchant.contract.model.ccb.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 新增商户文件上传
 * <AUTHOR>
 * @date 2021/8/3
 */
@Data
public class CcbFilesAdd {

    /**
     * 文件类型编码
     * 01-营业执照
     * 02-组织机构代码
     * 05-税务登记证或完税证明
     * 06-法定代表人或负责人证件
     * 09-租赁合同
     * 10-门店照片
     * 11-民办非企业单位证书
     * 12-水电缴费证明
     * 13-特许经营行业许可证
     * 14-ICP证
     * 18-结算账户证明文件
     * 19-财务状况证明文件
     * 20-个人征信查询授权书
     * 21-企业征信查询授权书
     * 22-合法资金管理关系证明
     * 99-其他
     */
    @JSONField(name = "FILE_ECD")
    private String fILEECD;

    /**
     * 文件名称
     */
    @JSONField(name = "Atch_File_Nm")
    private String atchFileNm;

    /**
     * 文件编号
     */
    @JSONField(name = "Bsn_ID")
    private String bsnID;

    /**
     * 文件大小
     */
    @JSONField(name = "Eltc_Files_File_Sz")
    private String eltcFilesFileSz;
}
