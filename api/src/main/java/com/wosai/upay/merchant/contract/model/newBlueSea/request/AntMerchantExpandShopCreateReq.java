package com.wosai.upay.merchant.contract.model.newBlueSea.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2020/11/24 09:34
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Accessors(chain = true)
@Builder
public class AntMerchantExpandShopCreateReq implements Serializable {

    /**
     * 经营地址。地址对象中省、市、区、地址必填，其余选填
     */
    @NotNull(message = "经营地址。地址对象中省、市、区、地址必填")
    @JSONField(name = "business_address")
    private AddressInfo businessAddress;

    /**
     * 店铺类目，取值参见文件https://mif-pub.alipayobjects.com/ShopCategory.xlsx 中的三级门店类目
     */
    @NotBlank(message = "店铺类目必填")
    @JSONField(name = "shop_category")
    @Length(max = 10)
    private String shopCategory;

    /**
     * 门店编号，表示该门店在该商户角色id(直连pid，间连smid)下，由商户自己定义的外部门店编号
     */
    @NotBlank(message = "门店编号必填")
    @JSONField(name = "store_id")
    @Length(max = 32)
    private String storeId;

    /**
     * 店铺经营类型，01表示直营，02表示加盟
     */
    @NotBlank(message = "店铺经营类型必填")
    @JSONField(name = "shop_type")
    @Length(max = 2)
    private String shopType;

    /**
     * 商户角色id，表示将要开的店属于哪个商户角色。对于直连开店场景，填写商户pid；对于间连开店场景（线上、线下、直付通），填写商户smid
     */
    @NotBlank(message = "商户角色id必填")
    @JSONField(name = "ip_role_id")
    @Length(max = 32)
    private String ipRoleId;

    /**
     * 店铺名称。直连开店要保证全局店铺名称+地址唯一，间连开店要保证服务商pid下店铺名称+地址唯一
     */
    @NotBlank(message = "店铺名称必填")
    @JSONField(name = "shop_name")
    @Length(max = 128)
    private String shopName;

    /**
     * 营业执照图片url。其值为使用ant.merchant.expand.indirect.image.upload上传图片得到的一串oss key。当店铺类目为特殊行业时必填
     */
    @JSONField(name = "cert_image")
    @Length(max = 256)
    private String certImage;

    /**
     * 法人名称。当店铺类目是特殊类目是要求必填
     */
    @JSONField(name = "legal_name")
    @Length(max = 64)
    private String legalName;

    /**
     * 法人身份证号。当店铺类目是特殊类目是要求必填
     */
    @JSONField(name = "legal_cert_no")
    @Length(max = 18)
    private String legalCertNo;

    /**
     * 营业执照授权函。其值为使用ant.merchant.expand.indirect.image.upload上传图片得到的一串oss key。当店铺类目是特殊行业时必填
     */
    @JSONField(name = "license_auth_letter_image")
    @Length(max = 256)
    private String licenseAuthLetterImage;

    /**
     * 店铺的联系固话，和店铺联系手机二选一必填
     */
    @JSONField(name = "contact_phone")
    @Length(max = 16)
    private String contactPhone;

    /**
     * 店铺联系手机，与店铺联系固话二选一必填
     */
    @NotBlank(message = "店铺联系手机必填")
    @JSONField(name = "contact_mobile")
    @Length(max = 16)
    private String contactMobile;

    /**
     * 证件号码。请填写店铺营业执照号。当店铺类目是特殊类目是要求必填
     */
    @JSONField(name = "cert_no")
    @Length(max = 32)
    private String certNo;

    /**
     * 证件类型，取值范围：201：营业执照；2011:多证合一(统一社会信用代码)。当店铺类目是特殊类目是要求必填
     */
    @JSONField(name = "cert_type")
    @Length(max = 10)
    private String certType;

    /**
     * 营业执照名称，填写值为营业执照或统一社会信用代码证上的名称。当店铺类目是特殊类目是要求必填
     */
    @JSONField(name = "cert_name")
    @Length(max = 64)
    private String certName;

    /**
     * 结算支付宝账号的登录号
     */
    @JSONField(name = "settle_alipay_logon_id")
    @Length(max = 64)
    private String settleAlipayLogonId;

    /**
     *备注
     */
    @Length(max = 256)
    @JSONField(name = "memo")
    private String memo;

    /**
     * 品牌id
     */
    @JSONField(name = "brand_id")
    @Length(max = 32)
    private String brandId;

    /**
     * 场景
     */
    @Length(max = 20)
    @JSONField(name = "scene")
    private String scene;

    /**
     * 门头照，其值为使用ant.merchant.expand.indirect.image.upload上传图片得到的一串oss key。当店铺类目是特殊类目是要求必填
     */
    @JSONField(name = "out_door_images")
    private List<String> outDoorImages;

    /**
     *行业特殊资质。当店铺类目是特殊类目是要求必填
     */
    @JSONField(name = "qualifications")
    private List<Qualifications> qualifications;

    /**
     * 店铺经营时间。
     */
    @JSONField(name = "business_time")
    private List<BusinessTime> businessTime;

    /**
     * 联系人信息。如果填写，其中名称必填，手机、固话、email三选一必填
     */
    @JSONField(name = "contact_infos")
    private List<ContactInfos> contactInfos;

    /**
     * 	扩展信息列表。key值需要向对应行业的bd进行申请。
     */
    @JSONField(name = "ext_infos")
    private List<ExtInfos> extInfos;

    /**
     * 门店结算卡信息。本业务当前只允许传入一张结算卡
     */
    @JSONField(name = "biz_cards")
    private List<BizCards> bizCards;

    @NoArgsConstructor
    @Data
    @Accessors(chain = true)
    public static class AddressInfo {
        /**
         *城市编码
         */
        @NotBlank(message = "城市编码必填")
        @JSONField(name = "city_code")
        @Length(max = 10)
        private String cityCode;
        /**
         *区县编码
         */
        @NotBlank(message = "区县编码必填")
        @JSONField(name = "district_code")
        @Length(max = 10)
        private String districtCode;
        /**
         * 地址。商户详细经营地址或人员所在地点
         */
        @NotBlank(message = "商户详细经营地址或人员所在地点必填")
        @JSONField(name = "address")
        @Length(max = 256)
        private String address;

        /**
         * 省份编码
         */
        @NotBlank(message = "省份编码必填")
        @JSONField(name = "province_code")
        @Length(max = 10)
        private String provinceCode;

        /**
         * 高德poiid
         */
        @JSONField(name = "poiid")
        @Length(max = 16)
        private String poiid;

        /**
         * 经度，浮点型, 小数点后最多保留6位。
         */
        @JSONField(name = "longitude")
        @Length(max = 11)
        private String longitude;

        /**
         * 纬度，浮点型,小数点后最多保留6位
         */
        @JSONField(name = "latitude")
        @Length(max = 10)
        private String latitude;

        /**
         *地址类型
         */
        @JSONField(name = "type")
        @Length(max = 32)
        private String type;
    }

    @NoArgsConstructor
    @Data
    @Accessors(chain = true)
    public static class Qualifications {
        /**
         * 商户行业资质类型，具体选值参见https://mif-pub.alipayobjects.com/QualificationType.xlsx
         */
        @JSONField(name = "industry_qualification_type")
        private String industryQualificationType;

        /**
         * 商户行业资质图片。其值为使用ant.merchant.expand.indirect.image.upload上传图片得到的一串oss key
         */
        @JSONField(name = "industry_qualification_image")
        private String industryQualificationImage;
    }

    @NoArgsConstructor
    @Data
    @Accessors(chain = true)
    public static class BusinessTime {

        /**
         * 本对象表示周几的营业时间。1~6表示周一到周六，7表示周日
         */
        @JSONField(name = "week_day")
        private Integer weekDay;

        /**
         *开门时间 格式：HH:mm
         */
        @JSONField(name = "open_time")
        private String openTime;

        /**
         *	关门时间 格式：HH:mm
         */
        @JSONField(name = "close_time")
        private String closeTime;
    }

    @NoArgsConstructor
    @Data
    @Accessors(chain = true)
    public static class ContactInfos {
        /**
         * 联系人名字
         */
        @JSONField(name = "name")
        private String name;

        /**
         * 电话
         */
        @JSONField(name = "phone")
        private String phone;

        /**
         * 手机号
         */
        @JSONField(name = "mobile")
        private String mobile;

        /**
         * 电子邮箱
         */
        @JSONField(name = "email")
        private String email;

        /**
         *联系人类型，取值范围：LEGAL_PERSON：法人；CONTROLLER：实际控制人；AGENT：代理人；OTHER：其他
         */
        @JSONField(name = "type")
        private String type;

        /**
         * 身份证号
         */
        @JSONField(name = "id_card_no")
        private String idCardNo;

        /**
         * 商户联系人业务标识枚举，表示商户联系人的职责。异议处理接口人:02;商户关键联系人:06;数据反馈接口人:11;服务联动接口人:08
         */
        @JSONField(name = "tag")
        private List<String> tag;
    }

    @NoArgsConstructor
    @Data
    @Accessors(chain = true)
    public static class ExtInfos {
        /**
         *KV数据对的key，表示该kv对象表示什么含义。
         */
        @JSONField(name = "key_name")
        private String keyName;

        /**
         *kv对象的值
         */
        @JSONField(name = "value")
        private String value;
    }

    @NoArgsConstructor
    @Data
    @Accessors(chain = true)
    public static class BizCards {
        /**
         * 卡户名
         */
        @JSONField(name = "account_holder_name")
        private String accountHolderName;

        /**
         * 银行卡号
         */
        @JSONField(name = "account_no")
        private String accountNo;

        /**
         * 开户行所在地-省
         */
        @JSONField(name = "account_inst_province")
        private String accountInstProvince;

        /**
         * 开户行所在地-市
         */
        @JSONField(name = "account_inst_city")
        private String accountInstCity;

        /**
         * 开户支行名
         */
        @JSONField(name = "account_branch_name")
        private String accountBranchName;

        /**
         * 账号使用类型
         * 对公-01
         * 对私-02
         */
        @JSONField(name = "usage_type")
        private String usageType;

        /**
         * 卡类型
         * 借记卡-DC
         * 信用卡-CC
         */
        @JSONField(name = "account_type")
        private String accountType;
        /**
         * 银行名称
         */
        @JSONField(name = "account_inst_name")
        private String accountInstName;

        /**
         * 开户行简称缩写
         */
        @JSONField(name = "account_inst_id")
        private String accountInstId;

        /**
         * 联行号
         */
        @JSONField(name = "bank_code")
        private String bankCode;
    }
}
