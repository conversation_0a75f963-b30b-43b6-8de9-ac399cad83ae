package com.wosai.upay.merchant.contract.model.fuyou;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 富友终端请求model
 * http://fundwx.fuiou.com/doc/#/scanentry/api_copy?id=_41-%e5%95%86%e6%88%b7%e4%bf%a1%e6%81%af%e7%99%bb%e8%ae%b0%e6%8e%a5%e5%8f%a3
 * <AUTHOR>
 * @date 2023-09-18
 */
@Data
@Accessors(chain = true)
public class TerminalOperateRequest {

    /**
     * 唯一流水号，机构自己定义，此字段可辅助拉取报文
     */
    private String trace_no;

    /**
     * 已在富友入网的富友商户代码 示例：0002900F0468631
     */
    private String mchnt_cd;

    /**
     * 商户号
     */
    private String merchant_sn;

    /**
     * 机构号,接入机构在富友的唯一代码
     */
    private String ins_cd;

    /**
     * 操作标识，取值范围：
     * 00：新增；
     * 01：修改；
     * 02：注销；
     * （注销状态，将会拒绝交易）
     */
    private String handle_type;


    /**
     * 富友终端型号
     * 详见附件（259）聚合终端类型V0.2-20220210版
     */
    private String tm_mode;

    /**
     * 终端设备状态(启用、注销)
     * 00：启用；
     * 01：注销；
     * （注销状态，将会拒绝交易）
     */
    private String tm_device_state;

    /**
     * 填写厂家提供的TUSN号，格式为：[8位数字(8位数字为：6位厂商编号+2位终端类型)+SN]，总长度大于等于16位，eg:00000104188SCA8U1514 注意：TUSN号一定要填写准确！
     *
     * 如果厂家没有TUSN或者没有实体终端，本字段则填写：
     * [mchnt_cd + SN号]或者[mchnt_cd + 合作方的虚拟终端号] eg:0002900F04686310000000001
     *
     * 此字段后续填入交易的reserved_terminal_info.serial_num字段中。
     */
    private String tm_serial_no;

    /**
     * 终端布放地(省-市-区-详细地址)
     * 例如：四川省-成都市-成华区-双林路339号成都339购物中心A座
     */
    private String tm_contact_addr;

    /**
     * 经度 (终端布放地)经度必须以W，E 开头
     */
    private String longitude;


    /**
     * 经度 (终端布放地)经度必须以W，E 开头
     */
    private String latitude;

    /**
     * 交易默认终端（1：是；0：否）不传默认为0
     */
    private String trade_default_term;

    /**
     *终端别称
     */
    private String tm_name_cn;



}
