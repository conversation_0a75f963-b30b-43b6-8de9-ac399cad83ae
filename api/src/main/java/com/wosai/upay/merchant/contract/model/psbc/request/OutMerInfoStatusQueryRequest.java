package com.wosai.upay.merchant.contract.model.psbc.request;

import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

/**
 * @Description: 外包机构通过移动商圈进行商户状态查询 请求参数
 * <AUTHOR>
 * @Date 2021/4/9 09:41
 */
@Data
@SuperBuilder
@Accessors(chain = true)
public class OutMerInfoStatusQueryRequest extends PsbcBaseRequest {
    /**
     * 交易码
     */
    private String reqCode;

    /**
     * 收单商户号 收单系统分配的商户号
     */
    @Length(max = 15)
    private String merId;

    /**
     * 外包商户号
     */
    @NotBlank(message = "外包商户号 不能为空")
    @Length(max = 15)
    private String outMerId;

}
