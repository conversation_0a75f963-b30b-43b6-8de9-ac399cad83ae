package com.wosai.upay.merchant.contract.service;

import com.wosai.upay.merchant.contract.model.chinaums.PicUploadRequestBO;
import com.wosai.upay.merchant.contract.model.chinaums.request.*;
import com.wosai.upay.merchant.contract.model.chinaums.ChinaUmsResponseWrap;
import com.wosai.upay.merchant.contract.model.chinaums.response.*;
import com.wosai.upay.merchant.contract.model.provider.ChinaUmsParam;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.Map;

/**
 * @Description: 银联商务自助签约详细采集接口
 * <AUTHOR>
 * @Date 2021/2/19 17:21
 */
@Validated
public interface ChinaUmsBiz {
    /**
    * @param  requestBO 请求基础数据
    * @param  param 收单机构相关参数
    * @return
    * @Author: zhmh
    * @Description: 后台图片上传接口
    * @time: 09:54 2021/2/22
    */
    ChinaUmsResponseWrap<PicUploadRequestBO, PicUploadResponse> picUpload(@Valid  PicUploadRequestBO requestBO, @Valid ChinaUmsParam param);

    /**
    * @Author: zhmh
    * @Description: 将入网用户档案资料信息上传,在图片上传接口完成后调用
    * @time: 16:43 2021/3/5
    */
    ChinaUmsResponseWrap<ComplexUploadRequest, ComplexUploadResponse> complexUpload(@Valid ComplexUploadRequest request, @Valid ChinaUmsParam param);

    /**
     * @Author: zhmh
     * @Description: 将入网用户档案资料信息上传完成以后组装url返回给前端
     * @time: 16:43 2021/3/5
     */
     String generateUrl( @Valid AgreementSignRequest request, @Valid ChinaUmsParam param);


   /**
   * @param
   * @return
   * @Author: zhmh
   * @Description:入网状态查询接口 全量状态流程——18：资料填写中（前端对接流程状态）--05：对公账户待验证或异常（对公账户状态）--00：签约中--01：签约成功--06：风控审核中（系统审核状态）/02：入网审核中--03：入网成功/04：入网失败
   * @time: 15:56 2021/3/4
   */
    ChinaUmsResponseWrap<ApplyQryRequest, ApplyQryResponse> applyQry(@Valid ApplyQryRequest request, @Valid ChinaUmsParam param);

    /**
    * @param
    * @return
    * @Author: zhmh
    * @Description:  所属支行查询接口
    * @time: 15:00 2021/3/8
    */
    ChinaUmsResponseWrap<BranchBankListRequest, BranchBankListResponse> branchBankList(@Valid BranchBankListRequest request, @Valid ChinaUmsParam param);

    /**
    * @param
    * @return
    * @Author: zhmh
    * @Description: 对公账户认证接口
    * @time: 15:19 2021/3/8
    */
    @Deprecated
    ChinaUmsResponseWrap<CompanyAccountVerifyRequest, ChinaUmsBaseResponse> companyAccountVerify(@Valid CompanyAccountVerifyRequest request, @Valid ChinaUmsParam param);

    /**
    * @param
    * @return
    * @Author: zhmh
    * @Description: 若平台提供的对公账户未收到验证打款，请平台调用此接口重新发起对公账户验证交易。正常流程资料上传接口调用成功后会自动触发对公打款，无需调用此流程
    * @time: 15:33 2021/3/8
    */
    @Deprecated
    ChinaUmsResponseWrap<RequestAccountVerifyRequest, ChinaUmsBaseResponse> requestAccountVerify(@Valid RequestAccountVerifyRequest request, @Valid ChinaUmsParam param);



    /**
     * @param
     * @return
     * @Author: zhmh
     * @Description: 微信子商户配置添加
     * @time: 15:33 2021/3/8
     */
    ChinaUmsResponseWrap<SubDevConfAddRequest, SubDevConfAddResponse> subDevConfAdd(@Valid SubDevConfAddRequest request, @Valid ChinaUmsParam param);


    /**
     * @param
     * @return
     * @Author: zhmh
     * @Description: 微信子商户配置查询
     * @time: 15:33 2021/3/8
     */
    ChinaUmsResponseWrap<SubDevConfQueryRequest, SubDevConfQueryResponse> subDevConfQuery(@Valid SubDevConfQueryRequest request, @Valid ChinaUmsParam param);



   /**
   * @Author: zhmh
   * @Description: 商户账户信息变更接口
   * @time: 09:21 2021/3/25
   */
    ChinaUmsResponseWrap<ComplexAlterAcctInfoRequest, ComplexAlterAcctInfoResponse> complexAlterAcctInfo(@Valid ComplexAlterAcctInfoRequest request, @Valid ChinaUmsParam param);


    /**
    * @Author: zhmh
    * @Description: 变更签约接口
    * @time: 10:56 2021/3/25
    */
    ChinaUmsResponseWrap<AlterSignRequest, AlterSignResponse> alterSign(@Valid AlterSignRequest request, @Valid ChinaUmsParam param);

    /**
     * @Author: zhmh
     * @Description: 变更入网状态查询接口
     * @time: 10:56 2021/3/25
     */
    ChinaUmsResponseWrap<AlterQryRequest, AlterQryResponse> alterQry(@Valid AlterQryRequest request, @Valid ChinaUmsParam param);


    /**
    * @Author: zhmh
    * @Description:微信商户进件接口
    * @time: 14:11 2021/3/25
    */
    ChinaUmsResponseWrap<GeneralMchntAddRequest, GeneralMchntAddResponse> generalMchntAdd(@Valid GeneralMchntAddRequest request, @Valid ChinaUmsParam param);


    /**
     * @Author: zhmh
     * @Description:微信商户进件确认接口
     * @time: 14:11 2021/3/25
     */
    ChinaUmsResponseWrap<GeneralMchntAddConfirmRequest, GeneralMchntAddConfirmResponse> generalMchntAddConfirm(@Valid GeneralMchntAddConfirmRequest request, @Valid ChinaUmsParam param);


    /**
     * @param
     * @return
     * @Author: zhmh
     * @Description: 微信子商户配置查询
     * @time: 15:33 2021/3/8
     */
    ChinaUmsResponseWrap<Object, Object> test1(@Valid Map request, @Valid ChinaUmsParam param);
}
