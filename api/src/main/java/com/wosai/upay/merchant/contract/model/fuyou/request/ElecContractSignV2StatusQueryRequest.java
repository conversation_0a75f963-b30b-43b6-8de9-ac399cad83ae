package com.wosai.upay.merchant.contract.model.fuyou.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * @Description: 签约状态查询接口
 * <AUTHOR>
 * @Date 2024/2/1 10:15
 */
@Data
public class ElecContractSignV2StatusQueryRequest {
    /** 机构号 */
    @JSONField(name = "ins_cd")
    private String insCd;

    /** 商户号 */
    @JSONField(name = "mchnt_cd")
    private String mchntCd;

    /** 流水号 */
    @JSONField(name = "trace_no")
    private String traceNo;

    /** 签约单号 */
    @JSONField(name = "contract_no")
    private String contractNo;

    /** 签名 */
    private String sign;
}
