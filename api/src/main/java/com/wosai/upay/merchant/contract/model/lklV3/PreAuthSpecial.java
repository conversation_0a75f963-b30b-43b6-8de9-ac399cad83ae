package com.wosai.upay.merchant.contract.model.lklV3;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description: 预授权请求data
 * <AUTHOR>
 * @Date 2023/7/24 下午3:01
 */
@NoArgsConstructor
@Data
public class PreAuthSpecial {

    @JSONField(name = "merCupNo")
    private String merCupNo;
    @JSONField(name = "merOptName")
    private String merOptName;
    @JSONField(name = "mobile")
    private String mobile;
    @JSONField(name = "installedAddress")
    private String installedAddress;
    @JSONField(name = "optType")
    private String optType;
    @JSONField(name = "tradeTypeInfos")
    private List<TradeTypeInfo> tradeTypeInfos;

    @NoArgsConstructor
    @Data
    public static class TradeTypeInfo {
        @JSONField(name = "termNo")
        private String termNo;
        @JSONField(name = "busiTypeId")
        private Integer busiTypeId;
        @JSONField(name = "productId")
        private Integer productId;
        @JSONField(name = "productName")
        private String productName;
        @JSONField(name = "deviceSn")
        private String deviceSn;
        @JSONField(name = "deviceMode")
        private String deviceMode;
    }
}
