package com.wosai.upay.merchant.contract.model.weixin.store.response;

import com.alibaba.fastjson.annotation.JSONField;
import com.wosai.upay.merchant.contract.model.weixin.store.FailedStoreRecipient;
import com.wosai.upay.merchant.contract.model.weixin.store.StoreRecipient;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2022/3/7 09:42
 */
@NoArgsConstructor
@Data
public class RecipientsBindResponse {

    @JSONField(name = "failed_store_recipient")
    private List<FailedStoreRecipient> failedStoreRecipient;
}
