package com.wosai.upay.merchant.contract.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.validation.NotEmpty;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import com.wosai.upay.merchant.contract.model.haike.AlipaySyncSpecialFeeRateParam;
import com.wosai.upay.merchant.contract.model.haike.HaikeTerminalRequest;
import com.wosai.upay.merchant.contract.model.haike.HaikeUnionSyncRequest;
import com.wosai.upay.merchant.contract.model.haike.MerchantAffiliationRequest;
import com.wosai.upay.merchant.contract.model.provider.*;
import com.wosai.upay.merchant.contract.model.terminal.AddTermInfoDTO;
import com.wosai.upay.merchant.contract.model.terminal.LogOutTermInfoDTO;
import com.wosai.upay.merchant.contract.model.terminal.UpdateTermInfoDTO;
import com.wosai.upay.merchant.contract.model.weixin.*;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.Map;

/**
 * 海科对接接口
 *
 * <AUTHOR>
 * @date 2023-07-10
 */
@JsonRpcService("/rpc/haike")
@Validated
public interface HaikeService {


    /**
     * 商户向收单机构进件
     * copy from com.wosai.upay.merchant.contract.service.HaikeServiceImpl#contractMerchant
     * 仅仅移除了校验的逻辑
     *
     * @param contextParam 上下文参数
     * @param haikeParam   海科参数
     * @return 报备结果
     */
    ContractResponse contractToAcquirer(Map contextParam, @Valid HaikeParam haikeParam);

    /**
     * 进件商户
     *
     * @param contextParam
     * @return
     */
    ContractResponse contractMerchant(Map contextParam, @Valid HaikeParam haikeParam);


    /**
     * 修改进件商户
     *
     * @param contextParam
     * @return
     */
    ContractResponse updateMerchant(Map contextParam, @Valid HaikeParam haikeParam);


    /**
     * 修改进件商户
     *
     * @param contextParam
     * @return
     */
    ContractResponse updateBasicMerchant(Map contextParam, @Valid HaikeParam haikeParam);


    /**
     * 修改进件商户
     *
     * @param contextParam
     * @return
     */
    ContractResponse updateBankAccountMerchant(Map contextParam, @Valid HaikeParam haikeParam);


    /**
     * 修改商户结算账户
     *
     * @param acquirerMerchantId 收单机构商户号
     * @param contextParam       参数上下文
     * @param haikeParam         haike交易参数
     * @return 结果
     */
    ContractResponse updateBankAccountMerchant(String acquirerMerchantId, Map contextParam, @Valid HaikeParam haikeParam);


    /**
     * 查询商户
     *
     * @param contextParam
     * @param haikeParam
     * @return
     */
    ContractResponse queryMerchant(Map contextParam, @Valid HaikeParam haikeParam);

    /**
     * 查询 银联商户报备结果
     *
     * @param providerMerchantId 收单机构商户号
     * @param haikeParam         海科参数
     * @return 查询结果
     */
    ContractResponse queryMerchantContractResult(@NotBlank String providerMerchantId, @Valid HaikeParam haikeParam);

    /**
     * 进件微信
     *
     * @param contextParam 上下文参数
     * @param wechatParam  报备参数
     * @return 返回结果
     */
    ContractResponse contractWeixinWithParams(Map contextParam, UnionWeixinParam wechatParam);

    /**
     * 更新微信
     *
     * @param contextParam 上下文参数
     * @param wechatParam  报备参数
     * @return 返回结果
     */
    ContractResponse updateWeixinWithParams(Map contextParam, @Valid UnionWeixinParam wechatParam);

    /**
     * 更新微信
     *
     * @param contextParam 上下文参数
     * @param wechatParam  报备参数
     * @return 返回结果
     */
    ContractResponse updateWechatBySubMchId(String subMchId, Map contextParam, UnionWeixinParam wechatParam);

    /**
     * 进件支付宝
     *
     * @param contextParam 上下文参数
     * @param alipayParam  报备参数
     * @return 返回结果
     */
    ContractResponse contractAlipayWithParams(Map contextParam, UnionAlipayParam alipayParam);


    /**
     * 查询微信商户信息
     *
     * @param subMchId
     * @return
     */
    MchInfo queryWeChatMchInfoParams(@NotEmpty(message = "微信商户号不能为空") String subMchId);

    /**
     * 更新支付宝
     *
     * @param contextParam 上下文参数
     * @param alipayParam  报备参数
     * @return 返回结果
     */
    ContractResponse updateAlipayWithParams(Map contextParam, UnionAlipayParam alipayParam);

    /**
     * 更新支付宝
     *
     * @param contextParam 上下文参数
     * @param alipayParam  报备参数
     * @return 返回结果
     */
    ContractResponse updateAlipayBySubMchId(String subMchId, Map contextParam, UnionAlipayParam alipayParam);


    /**
     * @return
     * <AUTHOR>
     * @Description:像银联查询支付宝子商户号信息
     * @time 9:56 上午
     **/
    Map queryAlySubMch(@NotEmpty(message = "支付宝子商户号不能为空") String subMchId);


    /**
     * 同步子商户信息去海科
     *
     * @param subMchId
     * @return
     */
    ContractResponse syncMerchantToHaike(String subMchId);

    /**
     * 同步终端信息到海科
     *
     * @param request
     * @param haikeParam
     * @return
     */
    ContractResponse syncTerminalToHaike(HaikeTerminalRequest request, @Valid HaikeParam haikeParam);


    /**
     * 同步终端信息到海科
     *
     * @param terminalId
     * @return
     */
    ContractResponse syncTerminalToHaike(String merchantSn, String terminalId);


    /**
     * 同步终端信息到海科
     *
     * @param terminalId
     * @return
     */
    ContractResponse syncTerminalToHaike(String merchantSn, String terminalId, Integer payway);


    /**
     * 新增微信终端信息
     *
     * @param dto
     * @param weixinParam
     * @return
     */
    ContractResponse addWxTermInfo(@Valid AddTermInfoDTO dto, @Valid UnionWeixinParam weixinParam);


    /**
     * 修改微信终端信息
     *
     * @param dto
     * @param weixinParam
     * @return
     */
    ContractResponse updateWxTermInfo(@Valid UpdateTermInfoDTO dto, @Valid UnionWeixinParam weixinParam);


    /**
     * 注销微信终端信息
     *
     * @param dto
     * @param weixinParam
     * @return
     */
    ContractResponse LogOutWxTermInfo(@Valid LogOutTermInfoDTO dto, @Valid UnionWeixinParam weixinParam);


    /**
     * 新增支付宝终端信息
     *
     * @param dto
     * @param alipayParam
     * @return
     */
    ContractResponse addAliTermInfo(@Valid AddTermInfoDTO dto, @Valid UnionAlipayParam alipayParam);


    /**
     * 修改支付宝终端信息
     *
     * @param dto
     * @param alipayParam
     * @return
     */
    ContractResponse updateAliTermInfo(@Valid UpdateTermInfoDTO dto, @Valid UnionAlipayParam alipayParam);


    /**
     * 注销支付宝终端信息
     *
     * @param dto
     * @param alipayParam
     * @return
     */
    ContractResponse LogOutAliTermInfo(@Valid LogOutTermInfoDTO dto, @Valid UnionAlipayParam alipayParam);


    /**
     * 微信子商户号相关配置
     *
     * @param:
     * @return:
     * @date: 17:34
     */
    Map weixinSubdevConfig(WeixinConfig weixinConfig, HaikeParam haikeParam);


    /**
     * 微信子商户号相关配置
     *
     * @param:
     * @return:
     * @date: 17:34
     */
    SubdevConfigResp queryWeixinSubdevConfig(String subMchId, HaikeParam haikeParam);


    /**
     * 优惠费率活动报名
     *
     * @param param param
     * @return channelId
     */
    ApplySpecialFeeRateResponse applyFeeRate(ApplySpecialFeeRateParam param, HaikeActivityParam haikeActivityParam);


    /**
     * 微信活动修改
     *
     * @param param
     * @param haikeActivityParam
     * @return
     */
    ApplySpecialFeeRateResponse modifyRateApply(ModifySpecialFeeRateParam param, HaikeActivityParam haikeActivityParam);

    /**
     * 查询申请单审核结果
     *
     * @param: weixinApplyStatusParam
     * @return:
     * @date: 11:37
     */
    ApplySpecialFeeRateQueryResponse queryRateApplyStatus(@NotEmpty(message = "业务申请单号不能为空") String applicationId, HaikeParam haikeParam) throws Exception;

    /**
     * 支付宝报名成功同步
     */
    ApplySpecialFeeRateResponse syncAlipayUniversityActivitySuccessResult(AlipaySyncSpecialFeeRateParam param, HaikeActivityParam haikeActivityParam);


    /**
     * 商户终端和商户信息同步
     *
     * @param subMchId
     * @return
     */
    ContractResponse syncTerminalAndMerchant(String subMchId);


    ContractResponse merchantAffiliation(MerchantAffiliationRequest request);

    /**
     * 开通海科饭卡支付
     *
     * @param merchantSn 商户号
     * @param fileName   文件名
     * @param fileUrl    文件路径
     * @param haikeParam 海科参数
     * @return 开通结果
     */
    ContractResponse openHaikeFoodCard(String merchantSn, String fileName, String fileUrl, HaikeParam haikeParam);
}
