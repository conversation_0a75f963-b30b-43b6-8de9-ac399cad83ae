package com.wosai.upay.merchant.contract.model.tlV2.request;

import com.wosai.upay.merchant.contract.model.tlV2.enume.RejectTrxCodeEnum;
import com.wosai.upay.merchant.contract.model.tlV2.enume.TerminalReqTypeEnum;
import com.wosai.upay.merchant.contract.model.tlV2.enume.TerminalStatusEnum;
import lombok.Data;

import java.util.List;

/**
 * 终端管理请求对象
 */
@Data
public class TerminalRequest {
    /**
     * 代理商号
     * 最大长度: 15
     * 平台分配的代理商号
     * 不可为空
     */
    private String orgid;

    /**
     * 商户号
     * 最大长度: 15
     * 审核通过分配的通联商户号
     * 不可为空
     */
    private String cusid;

    /**
     * 应用ID
     * 最大长度: 8
     * 平台分配的机构APPID
     * 不可为空
     */
    private String appid;

    /**
     * 版本号
     * 最大长度: 2
     * 接口版本号
     * 默认填11
     */
    private String version = "11";

    /**
     * 随机字符串
     * 最大长度: 32
     * 商户自行生成的随机字符串
     * 不可为空
     */
    private String randomstr;

    /**
     * 请求类型
     * 最大长度: 10
     * query/add
     * 不可为空
     * @see TerminalReqTypeEnum
     */
    private String reqtype;

    /**
     * 终端信息列表
     * 最大长度: 1000
     * 当reqtype为query时可为空
     */
    private String termlist;

    /**
     * 签名类型
     * RSA/SM2
     */
    private String signtype = "RSA";

    /**
     * 签名
     * 最大长度: 32
     * 详见安全规范
     * 不可为空
     */
    private String sign;

    /**
     * 终端信息
     */
    @Data
    public static class TerminalInfo {
        /**
         * 终端编号
         * 最大长度: 8
         * 当reqtype为add时不用填
         * 当reqtype为query时不为空
         */
        private String termcode;

        /**
         * 门店终端数
         * 最大长度: 1
         * 当reqtype为add时不为空
         * 单门店最大终端数:10
         */
        private String termnum;

        /**
         * 所属门店
         * 最大长度: 4
         * 不可为空
         */
        private String branchno;

        /**
         * 打印联数
         * 最大长度: 2
         * 默认为：2
         */
        private String printnum = "2";

        /**
         * 终端状态
         * 正常status=1/禁用status=0
         * 不可为空
         * @see TerminalStatusEnum
         */
        private String status;

        /**
         * 禁用交易类型
         * 最大长度: 100
         * 填入示例：VSP002，VSP003，VSP005
         * @see RejectTrxCodeEnum
         */
        private String rejecttrxcodes;
    }
}
