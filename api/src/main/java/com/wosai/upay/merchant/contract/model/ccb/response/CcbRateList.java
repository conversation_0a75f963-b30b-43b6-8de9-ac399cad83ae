package com.wosai.upay.merchant.contract.model.ccb.response;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/4
 */
@Data
public class CcbRateList {

    /**
     * POS终端编号
     */
    @JSONField(name = "POS_ID")
    private String posId;

    /**
     * 收单扣率类型代码
     */
    @JSONField(name = "Acq_CmsnChrgRate_TpCd")
    private String acqCmsnChrgRateTpCd;

    /**
     * 第一档次手续费率
     */
    @JSONField(name = "Fst_Lvl_HdCg_Rate")
    private String fstLvlHdCgRate;

    @JSONField(name = "FEE_DIS_LIST")
    private List<CcbFeeDisList> feeDisList;
}
