package com.wosai.upay.merchant.contract.model.guangfa;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Description: 广发请求参数/返回参数
 * <AUTHOR>
 * @Date: 2021/6/3 3:47 下午
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GFDTO<T> implements Serializable {
    /**
     * 请求头
     */

    @JSONField(name = "Header")
    @JsonProperty("Header")
    private GFHead head;
    /**
     * 请求体
     */
    @JSONField(name = "Body")
    @JsonProperty("Body")
    private T body;
}
