package com.wosai.upay.merchant.contract.constant;

/**
 * @Author: jerry
 * @date: 2019/4/26 13:52
 * @Description:
 */
public class ContractSubTaskTaskType {


    /** 拉卡拉特有**/
    /**
     * 卡变更
     */
    public static final Integer SUB_TASK_TASK_TYPE_CARD_UPDATE = 4;
    /**
     * 商户状态
     */
    public static final Integer SUB_TASK_TASK_TYPE_STATUS_UPDATE = 1;
    /**
     * 基本信息
     */
    public static final Integer SUB_TASK_TASK_TYPE_BASIC_INFORMATION = 0;
    /**
     * 结算账户变更
     */
    public static final Integer SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS = 2;
    /**
     * 商户费率变更
     */
    public static final Integer SUB_TASK_TASK_TYPE_MERCHANT_FEERATE = 3;


    /**
     * crm变更
     */
    public static final Integer SUB_TASK_TASK_TYPE_CRM_BOTH = 9;

    /**
     * 附件上传（不用）
     */
    public static final Integer SUB_TASK_TASK_TYPE_ATTACHMENT_UPLOADING = 99;

    /**
     * 进件
     */
    public static final Integer SUB_TASK_TASK_TYPE_CONTRACT = 5;
    /**
     * 更新营业执照
     */
    public static final Integer SUB_TASK_TASK_TYPE_UPDATE_BUSINESS_LICENSE= 13;

}
