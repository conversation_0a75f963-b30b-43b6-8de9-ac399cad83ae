package com.wosai.upay.merchant.contract.model.weixin.store.response;

import com.alibaba.fastjson.annotation.JSONField;
import com.wosai.upay.merchant.contract.model.weixin.store.FailedStoreRecipient;
import com.wosai.upay.merchant.contract.model.weixin.store.StoreRecipient;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/30
 */
@NoArgsConstructor
@Data
public class RecipientsUnBindResponse {

    /**
     * 绑定失败的门店收款信息
     */
    @JSONField(name = "failed_store_recipient")
    private List<FailedStoreRecipient> failedStoreRecipient;
}
