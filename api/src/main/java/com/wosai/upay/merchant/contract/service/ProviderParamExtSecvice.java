package com.wosai.upay.merchant.contract.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.bean.PageInfo;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@JsonRpcService("/rpc/providerParamExtSecvicee")
@Validated
public interface ProviderParamExtSecvice {

    /**

     *@描述

     *@参数  [pageInfo]  分页查询M3成功的数据

     *@返回值  java.util.List<java.util.Map<java.lang.String,java.lang.Object>>

     *@创建人  lishuangqiang

     *@创建时间  2020-05-27

     *@修改人和其它信息

     */
    List<Map<String, Object>> getMerchantProviderParamsExt(PageInfo pageInfo) ;

    }
