package com.wosai.upay.merchant.contract.model.zft;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description: 直付通商户创建业务参数
 * <AUTHOR>
 * @Date 2023/7/27 上午11:01
 */
@NoArgsConstructor
@Data
public class IndirectZftRequest {

    @JSONField(name = "cert_type")
    private String certType;
    @JSONField(name = "business_address")
    private BusinessAddress businessAddress;
    @JSONField(name = "legal_cert_front_image")
    private String legalCertFrontImage;
    @JSONField(name = "external_id")
    private String externalId;
    @JSONField(name = "mcc")
    private String mcc;
    @JSONField(name = "cert_image_back")
    private String certImageBack;
    @JSONField(name = "invoice_info")
    private InvoiceInfo invoiceInfo;
    @JSONField(name = "cert_image")
    private String certImage;
    @JSONField(name = "legal_name")
    private String legalName;
    @JSONField(name = "merchant_type")
    private String merchantType;
    @JSONField(name = "alipay_logon_id")
    private String alipayLogonId;
    @JSONField(name = "cert_no")
    private String certNo;
    @JSONField(name = "legal_cert_type")
    private String legalCertType;
    @JSONField(name = "sign_time_with_isv")
    private String signTimeWithIsv;
    @JSONField(name = "alias_name")
    private String aliasName;
    @JSONField(name = "default_settle_rule")
    private DefaultSettleRule defaultSettleRule;
    @JSONField(name = "license_auth_letter_image")
    private String licenseAuthLetterImage;
    @JSONField(name = "service_phone")
    private String servicePhone;
    @JSONField(name = "cert_name")
    private String certName;
    @JSONField(name = "legal_cert_no")
    private String legalCertNo;
    @JSONField(name = "binding_alipay_logon_id")
    private String bindingAlipayLogonId;
    @JSONField(name = "name")
    private String name;
    @JSONField(name = "legal_cert_back_image")
    private String legalCertBackImage;
    @JSONField(name = "sites")
    private List<SiteInfo> sites;
    @JSONField(name = "contact_infos")
    private List<ContactInfo> contactInfos;
    @JSONField(name = "out_door_images")
    private List<String> outDoorImages;
    @JSONField(name = "qualifications")
    private List<Qualification> qualifications;
    @JSONField(name = "in_door_images")
    private List<String> inDoorImages;
    @JSONField(name = "biz_cards")
    private List<BizCard> bizCards;
    @JSONField(name = "service")
    private List<String> service;

    @NoArgsConstructor
    @Data
    public static class BusinessAddress {
        @JSONField(name = "address")
        private String address;
        @JSONField(name = "district_code")
        private String districtCode;
        @JSONField(name = "latitude")
        private String latitude;
        @JSONField(name = "city_code")
        private String cityCode;
        @JSONField(name = "poiid")
        private String poiid;
        @JSONField(name = "province_code")
        private String provinceCode;
        @JSONField(name = "longitude")
        private String longitude;
    }

    @NoArgsConstructor
    @Data
    public static class InvoiceInfo {
        @JSONField(name = "mail_telephone")
        private String mailTelephone;
        @JSONField(name = "tax_payer_qualification")
        private String taxPayerQualification;
        @JSONField(name = "address")
        private String address;
        @JSONField(name = "accept_electronic")
        private Boolean acceptElectronic;
        @JSONField(name = "telephone")
        private String telephone;
        @JSONField(name = "title")
        private String title;
        @JSONField(name = "mail_name")
        private String mailName;
        @JSONField(name = "auto_invoice")
        private Boolean autoInvoice;
        @JSONField(name = "tax_payer_valid")
        private String taxPayerValid;
        @JSONField(name = "tax_no")
        private String taxNo;
        @JSONField(name = "bank_name")
        private String bankName;
        @JSONField(name = "mail_address")
        private MailAddress mailAddress;
        @JSONField(name = "bank_account")
        private String bankAccount;

        @NoArgsConstructor
        @Data
        public static class MailAddress {
            @JSONField(name = "address")
            private String address;
            @JSONField(name = "district_code")
            private String districtCode;
            @JSONField(name = "latitude")
            private String latitude;
            @JSONField(name = "city_code")
            private String cityCode;
            @JSONField(name = "poiid")
            private String poiid;
            @JSONField(name = "province_code")
            private String provinceCode;
            @JSONField(name = "type")
            private String type;
            @JSONField(name = "longitude")
            private String longitude;
        }
    }

    @NoArgsConstructor
    @Data
    public static class DefaultSettleRule {
        @JSONField(name = "default_settle_type")
        private String defaultSettleType;
        @JSONField(name = "default_settle_target")
        private String defaultSettleTarget;
    }

    @NoArgsConstructor
    @Data
    public static class SiteInfo {
        @JSONField(name = "site_name")
        private String siteName;
        @JSONField(name = "password")
        private String password;
        @JSONField(name = "site_type")
        private String siteType;
        @JSONField(name = "site_url")
        private String siteUrl;
        @JSONField(name = "account")
        private String account;

        @JSONField(name = "status")
        private String status;

        @JSONField(name = "auth_letter_image")
        private String authLetterImage;

        @JSONField(name = "screenshot_image")
        private String screenshotImage;

        @JSONField(name = "remark")
        private String remark;

        @JSONField(name = "remark_image")
        private String remarkImage;

        @JSONField(name = "site_domain")
        private String siteDomain;

        @JSONField(name = "icp_service_name")
        private String icpServiceName;

        @JSONField(name = "icp_no")
        private String icpNo;

        @JSONField(name = "icp_org_name")
        private String icpOrgName;

        @JSONField(name = "download")
        private String downloadUrl;

        @JSONField(name = "market")
        private String market;

        @JSONField(name = "tiny_app_id")
        private String tinyAppId;

    }

    @NoArgsConstructor
    @Data
    public static class ContactInfo {
        @JSONField(name = "id_card_no")
        private String idCardNo;
        @JSONField(name = "phone")
        private String phone;
        @JSONField(name = "name")
        private String name;
        @JSONField(name = "mobile")
        private String mobile;
        @JSONField(name = "email")
        private String email;
    }

    @NoArgsConstructor
    @Data
    public static class Qualification {
        @JSONField(name = "industry_qualification_image")
        private String industryQualificationImage;
        @JSONField(name = "industry_qualification_type")
        private String industryQualificationType;
    }

    @NoArgsConstructor
    @Data
    public static class BizCard {
        @JSONField(name = "account_inst_name")
        private String accountInstName;
        @JSONField(name = "bank_code")
        private String bankCode;
        @JSONField(name = "account_type")
        private String accountType;
        @JSONField(name = "usage_type")
        private String usageType;
        @JSONField(name = "account_holder_name")
        private String accountHolderName;
        @JSONField(name = "account_inst_city")
        private String accountInstCity;
        @JSONField(name = "account_inst_id")
        private String accountInstId;
        @JSONField(name = "account_no")
        private String accountNo;
        @JSONField(name = "account_inst_province")
        private String accountInstProvince;
        @JSONField(name = "account_branch_name")
        private String accountBranchName;
    }
}
